<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:22 BRT 2013 -->
<title>NewsletterModelImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="NewsletterModelImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/NewsletterModelImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/ParameterComponentFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/NewsletterModelImpl.html" target="_top">Frames</a></li>
<li><a href="NewsletterModelImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business</div>
<h2 title="Class NewsletterModelImpl" class="title">Class NewsletterModelImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>BusinessObjectImpl</li>
<li>
<ul class="inheritance">
<li>NewsletterModelImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">NewsletterModelImpl</span>
extends BusinessObjectImpl</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../com/visionnaire/webpublication/business/pss/PServantNewsletterModelImpl.html" title="class in com.visionnaire.webpublication.business.pss">PServantNewsletterModelImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#ps">ps</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#NewsletterModelImpl(com.visionnaire.PSS.pid)">NewsletterModelImpl</a></strong>(pid&nbsp;oid)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#NewsletterModelImpl(java.lang.String, java.util.Date, com.visionnaire.webpublication.business.SiteImpl)">NewsletterModelImpl</a></strong>(String&nbsp;name,
                   Date&nbsp;creationDate,
                   <a href="../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;site)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#getCreationDate()">getCreationDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#getName()">getName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../com/visionnaire/webpublication/business/NewsletterModelComponentImpl.html" title="class in com.visionnaire.webpublication.business">NewsletterModelComponentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#getNewsletterModelComponents()">getNewsletterModelComponents</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#getSite()">getSite</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#isUsed()">isUsed</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#pssDeleteFields()">pssDeleteFields</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#setCreationDate(java.util.Date)">setCreationDate</a></strong>(Date&nbsp;creationDate)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#setName(java.lang.String)">setName</a></strong>(String&nbsp;name)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html#setSite(com.visionnaire.webpublication.business.SiteImpl)">setSite</a></strong>(<a href="../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;site)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_BusinessObjectImpl">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;BusinessObjectImpl</h3>
<code>getPid, pssServant, setPS</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, getClassId, hashCode, isDying, pssDelete, pssDestroy, pssLock, toInternal, toInternal, toString, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ps">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ps</h4>
<pre>private final&nbsp;<a href="../../../../com/visionnaire/webpublication/business/pss/PServantNewsletterModelImpl.html" title="class in com.visionnaire.webpublication.business.pss">PServantNewsletterModelImpl</a> ps</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="NewsletterModelImpl(com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NewsletterModelImpl</h4>
<pre>public&nbsp;NewsletterModelImpl(pid&nbsp;oid)</pre>
</li>
</ul>
<a name="NewsletterModelImpl(java.lang.String, java.util.Date, com.visionnaire.webpublication.business.SiteImpl)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NewsletterModelImpl</h4>
<pre>public&nbsp;NewsletterModelImpl(String&nbsp;name,
                   Date&nbsp;creationDate,
                   <a href="../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;site)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssDeleteFields()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssDeleteFields</h4>
<pre>public&nbsp;void&nbsp;pssDeleteFields()
                     throws NotDisconnectedException</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in class&nbsp;<code>PersistentImplBase</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>NotDisconnectedException</code></dd></dl>
</li>
</ul>
<a name="getName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;String&nbsp;getName()</pre>
</li>
</ul>
<a name="setName(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(String&nbsp;name)</pre>
</li>
</ul>
<a name="getCreationDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreationDate</h4>
<pre>public&nbsp;Date&nbsp;getCreationDate()</pre>
</li>
</ul>
<a name="setCreationDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreationDate</h4>
<pre>public&nbsp;void&nbsp;setCreationDate(Date&nbsp;creationDate)</pre>
</li>
</ul>
<a name="getSite()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSite</h4>
<pre>public&nbsp;<a href="../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;getSite()</pre>
</li>
</ul>
<a name="setSite(com.visionnaire.webpublication.business.SiteImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSite</h4>
<pre>public&nbsp;void&nbsp;setSite(<a href="../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;site)</pre>
</li>
</ul>
<a name="getNewsletterModelComponents()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewsletterModelComponents</h4>
<pre>public&nbsp;PList&lt;<a href="../../../../com/visionnaire/webpublication/business/NewsletterModelComponentImpl.html" title="class in com.visionnaire.webpublication.business">NewsletterModelComponentImpl</a>&gt;&nbsp;getNewsletterModelComponents()</pre>
</li>
</ul>
<a name="isUsed()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isUsed</h4>
<pre>public&nbsp;boolean&nbsp;isUsed()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/NewsletterModelImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/ParameterComponentFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/NewsletterModelImpl.html" target="_top">Frames</a></li>
<li><a href="NewsletterModelImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
