<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:27 BRT 2013 -->
<title>Uses of Class com.visionnaire.webpublication.business.visualcontent.ContatoCRMImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.visionnaire.webpublication.business.visualcontent.ContatoCRMImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/\class-useContatoCRMImpl.html" target="_top">Frames</a></li>
<li><a href="ContatoCRMImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.visionnaire.webpublication.business.visualcontent.ContatoCRMImpl" class="title">Uses of Class<br>com.visionnaire.webpublication.business.visualcontent.ContatoCRMImpl</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.visionnaire.webpublication.business">com.visionnaire.webpublication.business</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.visionnaire.webpublication.web.engine">com.visionnaire.webpublication.web.engine</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.visionnaire.webpublication.business">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a> in <a href="../../../../../../com/visionnaire/webpublication/business/package-summary.html">com.visionnaire.webpublication.business</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/package-summary.html">com.visionnaire.webpublication.business</a> that return <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a></code></td>
<td class="colLast"><span class="strong">VisualContentFacade.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/VisualContentFacade.html#createContatoCRM(long, java.util.Date, java.util.Date, int, java.lang.Long, java.lang.Long, boolean, java.util.List, java.lang.String, java.lang.Long, java.lang.Long, java.util.List, java.util.List, java.util.List, java.lang.String, java.lang.String, java.util.List, java.util.List, com.visionnaire.webpublication.business.visualcontent.VisualContentImpl, java.lang.String, int, boolean, boolean, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, boolean, boolean, long, boolean, boolean, boolean, boolean, java.util.List, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String)">createContatoCRM</a></strong>(long&nbsp;componentPid,
                Date&nbsp;editionDate,
                Date&nbsp;removeDate,
                int&nbsp;priority,
                Long&nbsp;groupNamePid,
                Long&nbsp;workFlowPid,
                boolean&nbsp;approved,
                List&lt;Long&gt;&nbsp;tiedContents,
                String&nbsp;ipAddress,
                Long&nbsp;frontImageId,
                Long&nbsp;frontFileId,
                List&lt;Long&gt;&nbsp;anexosIds,
                List&lt;Long&gt;&nbsp;imagesId,
                List&lt;Long&gt;&nbsp;filesId,
                String&nbsp;credentials,
                String&nbsp;sessionId,
                List&lt;Long&gt;&nbsp;selectedTags,
                List&lt;String&gt;&nbsp;typedTags,
                <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;fatherContent,
                String&nbsp;url,
                int&nbsp;urlExternal,
                boolean&nbsp;isNewWindow,
                boolean&nbsp;isToShow,
                String&nbsp;emailResponsavel,
                String&nbsp;body,
                String&nbsp;internalTitle,
                String&nbsp;contentDescription,
                String&nbsp;hat,
                String&nbsp;contentAuthorName,
                String&nbsp;contentAuthorEmail,
                String&nbsp;urlContentPid,
                boolean&nbsp;preview,
                boolean&nbsp;wiki,
                long&nbsp;officialContentPid,
                boolean&nbsp;allowCommentaries,
                boolean&nbsp;preModeracao,
                boolean&nbsp;gerarArquivoDetalhe,
                boolean&nbsp;serEncontradoBusca,
                List&lt;Long&gt;&nbsp;imagesAnexasIds,
                String&nbsp;title,
                String&nbsp;end_consultorio,
                String&nbsp;cep,
                String&nbsp;tel_celular,
                String&nbsp;tel_comercial,
                String&nbsp;cidade,
                String&nbsp;convenios,
                String&nbsp;horarios,
                String&nbsp;email,
                String&nbsp;dias)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a></code></td>
<td class="colLast"><span class="strong">VisualContentFacade.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/VisualContentFacade.html#updateContatoCRM(long, java.util.Date, java.util.Date, int, java.lang.Long, boolean, java.lang.String, java.util.List, java.lang.String, java.lang.Long, java.lang.Long, java.util.List, java.util.List, java.util.List, java.util.List, java.util.List, java.lang.String, java.lang.String, java.lang.String, int, boolean, boolean, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, boolean, boolean, boolean, boolean, boolean, boolean, java.util.List, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String)">updateContatoCRM</a></strong>(long&nbsp;pid,
                Date&nbsp;editionDate,
                Date&nbsp;removeDate,
                int&nbsp;priority,
                Long&nbsp;groupNamePid,
                boolean&nbsp;approved,
                String&nbsp;justification,
                List&lt;Long&gt;&nbsp;tiedContents,
                String&nbsp;ipAddress,
                Long&nbsp;frontImageId,
                Long&nbsp;frontFileId,
                List&lt;Long&gt;&nbsp;anexosIds,
                List&lt;Long&gt;&nbsp;imagesId,
                List&lt;Long&gt;&nbsp;filesId,
                List&lt;Long&gt;&nbsp;selectedTags,
                List&lt;String&gt;&nbsp;typedTags,
                String&nbsp;credentials,
                String&nbsp;sessionId,
                String&nbsp;url,
                int&nbsp;urlExternal,
                boolean&nbsp;isNewWindow,
                boolean&nbsp;isToShow,
                String&nbsp;emailResponsavel,
                String&nbsp;body,
                String&nbsp;internalTitle,
                String&nbsp;contentDescription,
                String&nbsp;hat,
                String&nbsp;contentAuthorName,
                String&nbsp;contentAuthorEmail,
                String&nbsp;urlContentPid,
                boolean&nbsp;preview,
                boolean&nbsp;wiki,
                boolean&nbsp;allowCommentaries,
                boolean&nbsp;preModeracao,
                boolean&nbsp;gerarArquivoDetalhe,
                boolean&nbsp;serEncontradoBusca,
                List&lt;Long&gt;&nbsp;imagesAnexasIds,
                String&nbsp;title,
                String&nbsp;end_consultorio,
                String&nbsp;cep,
                String&nbsp;tel_celular,
                String&nbsp;tel_comercial,
                String&nbsp;cidade,
                String&nbsp;convenios,
                String&nbsp;horarios,
                String&nbsp;email,
                String&nbsp;dias)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.visionnaire.webpublication.web.engine">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a> in <a href="../../../../../../com/visionnaire/webpublication/web/engine/package-summary.html">com.visionnaire.webpublication.web.engine</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/web/engine/package-summary.html">com.visionnaire.webpublication.web.engine</a> that return <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a></code></td>
<td class="colLast"><span class="strong">ContatoCRMEngine.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/web/engine/ContatoCRMEngine.html#getObject()">getObject</a></strong>()</code>
<div class="block">(non-Javadoc)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a></code></td>
<td class="colLast"><span class="strong">ContatoCRMEngine.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/web/engine/ContatoCRMEngine.html#saveObject()">saveObject</a></strong>()</code>
<div class="block">(non-Javadoc)</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/web/engine/package-summary.html">com.visionnaire.webpublication.web.engine</a> that return types with arguments of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected List&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ContatoCRMImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">ContatoCRMEngine.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/web/engine/ContatoCRMEngine.html#getObjects(int, int, boolean)">getObjects</a></strong>(int&nbsp;begin,
          int&nbsp;end,
          boolean&nbsp;paginate)</code>
<div class="block">(non-Javadoc)</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/\class-useContatoCRMImpl.html" target="_top">Frames</a></li>
<li><a href="ContatoCRMImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
