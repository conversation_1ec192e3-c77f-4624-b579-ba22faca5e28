<?xml version = '1.0' encoding = 'UTF-8'?>
<Table class="oracle.dbtools.crest.model.design.relational.Table" directorySegmentName="seg_0" id="98229D52-DBDC-BD94-23F5-E54C70CD29AB" name="webp_researchquestion">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<allowColumnReorder>false</allowColumnReorder>
<existDependencyGenerateInDDl>true</existDependencyGenerateInDDl>
<columns itemClass="oracle.dbtools.crest.model.design.relational.Column">
<Column name="pid" id="6C1CA787-A1ED-9FDB-7970-645888125D5F">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="klass" id="D14A9357-668B-7415-5069-008DEE6913DE">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="research" id="6363270E-8136-6C28-1301-E448850EE766">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<delegate>19849FE5-C81F-0866-74EB-04F4E5F04653</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="D527A8FD-B35D-0CCD-D53E-C9BA3EF526D4" referredColumn="19849FE5-C81F-0866-74EB-04F4E5F04653"/>
</associations>
</Column>
<Column name="research_k" id="A2FCCB4F-C095-5ADA-7EE5-0BD5A28A450C">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="numberQuestion" id="3FE2492E-F542-E0F1-DE83-7EB79D964FF6">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<domain>08CE0E8A-3E6E-B83C-2C4C-8BD49EE40A28</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="question" id="E4D0A81B-4A50-4CC2-4DA7-15C3192A588F">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<domain>0ABCA146-80B1-5F1C-4734-BF515FF363AA</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="typeQuestion" id="CEEB1BF5-F466-6888-0F8A-EDCF28E99AD2">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<domain>08CE0E8A-3E6E-B83C-2C4C-8BD49EE40A28</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="options" id="3F2343FB-DA11-693B-A189-1A3937668C86">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<use>0</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<domain>4FCA7058-78BE-E83D-EBC4-E85D2E72DF20</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="requiredQuestion" id="8EB2670F-8BA2-EE2F-174D-B85D5CBB7706">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<domain>9248D72E-1B8A-6580-F0B9-B791FC078088</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="questionFather" id="599DCAF4-008A-B305-0920-D8E5E151B7FD">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<delegate>6C1CA787-A1ED-9FDB-7970-645888125D5F</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="64190F4F-12C7-A40B-ADE8-D830B1543DB3" referredColumn="6C1CA787-A1ED-9FDB-7970-645888125D5F"/>
</associations>
</Column>
<Column name="questionFather_k" id="3E4FE9FE-C972-2658-0DD8-A7CA78DF287A">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="kondition" id="BDC70CDD-0D43-57D0-61AC-0B5E221CE0F7">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<use>0</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<domain>A290A092-BE00-640C-8B42-01F81D650735</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="uniqueResponse" id="3112FA67-BCFD-9882-325C-34BFEB6A5169">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:53 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<defaultValue>(&apos;F&apos;)</defaultValue>
<use>0</use>
<logicalDatatype>LOGDT025</logicalDatatype>
<domain>9A1EDEB3-6AF2-6E40-E408-FEE768FD4580</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
</columns>
<indexes itemClass="oracle.dbtools.crest.model.design.relational.Index">
<ind_PK_UK id="BAB48B47-DC4B-FE64-10B1-7154B194FF5A" name="webp_researchquestion_pk">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<pk>true</pk>
<indexState>Primary Constraint</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="6C1CA787-A1ED-9FDB-7970-645888125D5F"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="FD976E31-26B2-365F-84B7-62724AD8371C" name="webp_researchquestion_u">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<indexState>Unique Constraint</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="6363270E-8136-6C28-1301-E448850EE766"/>
<colUsage columnID="3FE2492E-F542-E0F1-DE83-7EB79D964FF6"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="3723B008-1525-1095-64A8-C170AEF3FFCC" name="i_webp_rq_research">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="6363270E-8136-6C28-1301-E448850EE766"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="2A736FF5-95B0-9CA4-C31E-A62E5F3B7703" name="i_webp_rq_research_k">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="A2FCCB4F-C095-5ADA-7EE5-0BD5A28A450C"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="C1D77068-E09A-F74B-52AF-D642D4CE12E4" name="i_webp_rq_questionFather">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="599DCAF4-008A-B305-0920-D8E5E151B7FD"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="155E3D27-5AA7-FA2A-3F7D-1BE53E793FDC" name="i_webp_rq_questionFather_k">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="3E4FE9FE-C972-2658-0DD8-A7CA78DF287A"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="9A9D8DBE-99D6-AA5D-0FDC-72F3055659F8" name="webp_researchquestion_fk">
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<indexState>Foreign Key</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="6363270E-8136-6C28-1301-E448850EE766"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="5530C81D-25EC-4B19-28FA-806BCEBD3E89" name="webp_researchquestion_fk2">
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<indexState>Foreign Key</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="599DCAF4-008A-B305-0920-D8E5E151B7FD"/>
</indexColumnUsage>
</ind_PK_UK>
</indexes>
</Table>