<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1" errorPage="../error.jsp" %>   
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
		
	<!-- ContentCommonJQueryLibs -->
	<script type="text/javascript" src="libjs/jquery-ui-1.8.18.custom/js/jquery-1.7.1.min.js"></script>
	
	<link href="css/adm_list_tiles.css" type="text/css" rel="stylesheet" />
	<link href="css/adm_defaults_tiles.css" type="text/css" rel="stylesheet" />
	<link href="css/tooltip.css" type="text/css" rel="stylesheet" />

	<script type="text/javascript" src="libjs/news.js"></script>
	<script type="text/javascript" src="libjs/wiki.js"></script>
	<script type="text/javascript" src="libjs/util.js"></script>
	
	<!-- script type="text/javascript" src="uploadify/swfobject.js"></script>
	<script type="text/javascript" src="uploadify/jquery.uploadify.v2.1.4.min.js"></script>
	<link rel="stylesheet" href="uploadify/uploadify.css" type="text/css" media="screen"/ -->
	
	<script>
		/**
		 * Executado no onload do body.
		 */
		function initForm()
		{
			document.getElementById('txtTitle').focus();
		}
	</script>
	
	<!-- <body onload="initForm();"> -->
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 120, 'dicaTitulo');"/>
										<script>contadorCaracteres(document.forms[0].title, 120, 'dicaTitulo');</script>
										<span class="dica" id="dicaTitulo">120 caracteres</span>
									</fieldset>
								</div>
							</div>
						</div>
					<!-- ContentCommonDetails -->
					<jsp:include page="contentCommonDetails_tiles.jsp"/>
					</div>
				</div>
			</form>
		</div>
		<div id="dropzone" class="dropzone">
			<div class="dz-message">Arraste e solte imagens aqui ou clique para selecionar</div>
		</div>
		<button type="button" class="btn btn-primary" onclick="openImageUploadThumbs()">
			<i class="fa fa-plus"></i> Adicionar imagens
		</button>
		<script type="text/javascript" src="libjs/wz_tooltip.js"></script>
		<div id="container">
			<form name="contentForm" method="post">
				<jsp:include page="infoMessage_tiles.jsp"/>
				<div class="row">
					<div class="col-md-5 col-sm-4 col-xs-4 col-mb-12">
						<h1 class="page-header">${componentName}</h1>
					</div>	 
					<!-- ContentButtonsColumn -->
					<jsp:include page="contentButtonsColumn_tiles.jsp"/>
				</div>
				<div id="main_column">
					<div class="row"> 
						<div class="col-xs-12 ui-sortable">	
							<div class="panel panel-inverse" data-sortable-id="form-stuff-1" data-init="true">
								<div class="panel-heading">
									<div class="panel-heading-btn">
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-default" data-click="panel-expand"><i class="fa fa-expand"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-success" data-click="panel-reload"><i class="fa fa-repeat"></i></a> 
										<a href="javascript:;" class="btn btn-xs btn-icon btn-circle btn-warning" data-click="panel-collapse" data-original-title="" title=""><i class="fa fa-minus"></i></a>
									</div>
									<h4 class="panel-title" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T&iacute;tulo *</h4>
								</div>
								<div class="panel-body">
									<fieldset class="intro" id="titulo">
										<div id="tooltip_titulo" style="display: none;">
											<div class="Tooltip">
												<p>Deve ter significado completo (n�o depender da gravata para o entendimento) e utilizar as principais palavras usadas na p�gina.</p>
												<!-- <img src="image/tooltip/titulo.gif" /> -->
											</div>
										</div>
										<label style="margin-top: 0;" for="dicaTitulo" onmouseover="TagToTip('tooltip_titulo', BGCOLOR, '#f8fafc', BORDERCOLOR, '#c9d7e6');" onmouseout="UnTip();">T�tulo *</label>
										<input type="text" id="txtTitle" name="title" tabindex="1" class="form-control" maxlength="120" value="<c:out value="${title}"/>" onkeyup="contadorCaracteres(this, 12
