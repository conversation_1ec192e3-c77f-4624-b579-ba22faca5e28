<?xml version = '1.0' encoding = 'UTF-8'?>
<Table class="oracle.dbtools.crest.model.design.relational.Table" directorySegmentName="seg_0" id="F47200DD-0E06-3E0C-ED1D-D754B554A435" name="webp_dicas_intranetuser">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<allowColumnReorder>false</allowColumnReorder>
<existDependencyGenerateInDDl>true</existDependencyGenerateInDDl>
<columns itemClass="oracle.dbtools.crest.model.design.relational.Column">
<Column name="pid" id="CA52C3C4-9065-4C57-58B4-D95E171E2B62">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="klass" id="9E1E27D4-D707-C252-D401-AE0D0778C6B0">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="dica" id="812A57DF-18D5-69CA-A3E2-C4C42B4D0527">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<delegate>928F5AC8-8497-75B6-0108-C369FFF7B7CE</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="939F0B88-C03D-08A1-4643-CF4F042EF1B4" referredColumn="928F5AC8-8497-75B6-0108-C369FFF7B7CE"/>
</associations>
</Column>
<Column name="dica_k" id="17360A42-F678-55ED-CA9B-C923024D45B3">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="userDicas" id="C37399DC-7416-1640-9D38-568BEAF728B1">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
</columns>
<indexes itemClass="oracle.dbtools.crest.model.design.relational.Index">
<ind_PK_UK id="081B4B79-2FD7-A541-373D-30F47365C483" name="webp_dicas_intranetuser_pk">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<pk>true</pk>
<indexState>Primary Constraint</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="CA52C3C4-9065-4C57-58B4-D95E171E2B62"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="828ACD7A-4AB3-386D-5217-082239BF5228" name="webp_dicas_intranetuser_fk">
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<indexState>Foreign Key</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="812A57DF-18D5-69CA-A3E2-C4C42B4D0527"/>
</indexColumnUsage>
</ind_PK_UK>
</indexes>
</Table>