<?xml version="1.0" encoding="UTF-8" ?>
<settings>
	<logical_type_for_domain_presentation value="false" />
	<automatic_pk_generation value="false" />
	<automatic_uk_generation value="false" />
	<automatic_fk_generation value="false" />
	<preserve_ddl_generation_options value="false" />
	<substitution_patterns>
	</substitution_patterns>
	<classification_types>
		<type name="Fato" color="-7482" prefix="" id="1" />
		<type name="Dimensão" color="-1781507" prefix="" id="2" />
		<type name="Registrando" color="-1776412" prefix="" id="3" />
		<type name="Resumo" color="-3148598" prefix="" id="4" />
		<type name="Temporário" color="-1" prefix="" id="5" />
	</classification_types>
	<default_fonts_and_colors>
		<fc_object classname="Entity" background="-5971457" foreground="-16776961">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Atributo" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tipo de dados" font_color="-16744448" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Elemento da Chave Primária" font_color="-16776961" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Elemento da Chave Estrangeira" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Elemento de Chave Exclusiva" font_color="-16776961" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="NOT NULL" font_color="-65536" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Chave" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Logical View" background="-25750" foreground="-16776961">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Atributo" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tipo de dados" font_color="-16744448" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Table" background="-76" foreground="-16776961">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Coluna" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tipo de dados" font_color="-16744448" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Elemento da Chave Primária" font_color="-16776961" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Elemento da Chave Estrangeira" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Elemento de Chave Exclusiva" font_color="-16776961" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="NOT NULL" font_color="-65536" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Chave" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Relational View" background="-6881386" foreground="-16776961">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Coluna" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tipo de dados" font_color="-16744448" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Structured Type" background="-7537956" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Atributo" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tipo de dados" font_color="-16777056" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Método" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Não Instanciável" font_color="-65536" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Obrigatório" font_color="-65536" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Cube" background="-7482" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Entidades de Fato" font_color="-16776961" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tipo de Medida" font_color="-16776961" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Medida" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Função" font_color="-16777056" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Fórmula" font_color="-65536" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Atributos de Filho para Pai" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Dimension" background="-16713196" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
		</fonts>
		</fc_object>
		<fc_object classname="Level" background="-1781507" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Entidade do Nível" font_color="-16776961" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tipo" font_color="-16776961" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Atributo" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Função" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Process" background="-106" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Número do Processo" font_color="-65536" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tarefa de Transformação" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="External Agent" background="-5570646" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
		</fonts>
		</fc_object>
		<fc_object classname="Information Store" background="-10170881" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16776961" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Número" font_color="-1" font_name="Dialog" font_size="10" font_style="1"/>
		</fonts>
		</fc_object>
		<fc_object classname="In-Out Parameters" background="-328966" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16777216" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Parâmetros" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tipo de dados" font_color="-65536" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Transformation" background="-43" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16777216" font_name="Dialog" font_size="10" font_style="1"/>
			<font_object fo_type="Número do Processo" font_color="-65536" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Note" background="-4144960" foreground="-16777216">
		<fonts>
			<font_object fo_type="Título" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Label" background="-1" foreground="-16777216">
		<fonts>
			<font_object fo_type="Texto" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Relationship Attributes" background="-26266" foreground="-16777216">
		<fonts>
			<font_object fo_type="Atributo" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="Tipo de dados" font_color="-16744448" font_name="Dialog" font_size="10" font_style="0"/>
			<font_object fo_type="NOT NULL" font_color="-65536" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
		<fc_object classname="Legend" background="-1" foreground="-16777216">
		<fonts>
			<font_object fo_type="Texto" font_color="-16777216" font_name="Dialog" font_size="10" font_style="0"/>
		</fonts>
		</fc_object>
	</default_fonts_and_colors>
	<default_line_widths_and_colors>
		<lwc_object classname="Logical Relation" color="-16777216" width="1">
		</lwc_object>
		<lwc_object classname="Logical Inheritance" color="-65536" width="1">
		</lwc_object>
		<lwc_object classname="Relational Foreign Key" color="-16777216" width="1">
		</lwc_object>
		<lwc_object classname="Type Substitution" color="-16725996" width="1">
		</lwc_object>
		<lwc_object classname="Datatype Reference" color="-16776961" width="1">
		</lwc_object>
		<lwc_object classname="Datatype Inheritance" color="-65536" width="1">
		</lwc_object>
		<lwc_object classname="Multidimentional Link" color="-16776961" width="1">
		</lwc_object>
		<lwc_object classname="Multidimensional Hierarchy" color="-16725996" width="1">
		</lwc_object>
		<lwc_object classname="Process Flow" color="-65536" width="1">
		</lwc_object>
	</default_line_widths_and_colors>
	<naming_standard_rules>
		<logical>
			<separator value= "Space" char=" "/>
			<entity>
			</entity>
			<attribute>
			</attribute>
		</logical>
		<relational>
			<separator value= "_" abbreviated_only="false"/>
			<table>
			</table>
			<column>
			</column>
		</relational>
		<domains>
			<separator value= " "/>
			<domain>
			</domain>
		</domains>
		<constraints>
			<pk value="{table}_PK"/>
			<fk value="{child}_{parent}_FK"/>
			<ck value="{table}_CK"/>
			<un value="{table}_{column}_UN"/>
			<idx value="{table}_{column}_IDX"/>
			<colck value="CK_{table}_{column}"/>
			<column_foreign_key value="{ref table}_{ref column}"/>
			<ui value="{entity} PK"/>
			<relation_attribute value="{ref entity}_{ref attribute}"/>
			<surrogate_key value="{table abbr}_PK"/>
			<surrogate_key_col value="{table abbr}_ID"/>
			<discriminator_col value="{table abbr}_TYPE"/>
		</constraints>
		<glossaries>
		</glossaries>
	</naming_standard_rules>
	<comparemapping>
	</comparemapping>
	<engineering_params>
		<delete_without_origin value="false"/>
		<engineer_coordinates value="true"/>
		<engineer_generated value="false"/>
		<show_engineering_intree value="false"/>
		<apply_naming_std value="true"/>
		<use_pref_abbreviation value="true"/>
		<upload_directory value=""/>
		<date_format value="YYYY/MM/DD HH24:MI:SS"/>
		<timestamp_format value="YYYY/MM/DD HH24:MI:SS.FF"/>
		<timestamp_tz_format value="YYYY/MM/DD HH24:MI:SS.FFTZH:TZM"/>
	</engineering_params>
	<eng_compare show_sel_prop_only="true" not_apply_for_new_objects="true" exclude_from_tree="false">
		<entity_table>
			<property name="Nome" selected="true"/>
			<property name="Nome Curto/Abreviação" selected="true"/>
			<property name="Comentário" selected="true"/>
			<property name="Comentário no RDBMS" selected="true"/>
			<property name="Observações" selected="true"/>
			<property name="Escopo da Tabela Temporária" selected="true"/>
			<property name="Tipo de Tabela" selected="true"/>
			<property name="Tipo Estruturado" selected="true"/>
			<property name="Substituição de Tipo (Objeto do Tipo e Super)" selected="true"/>
			<property name="Volumes Mín." selected="true"/>
			<property name="Volumes Esperados" selected="true"/>
			<property name="Volumes Máx." selected="true"/>
			<property name="Percentual de Crescimento" selected="true"/>
			<property name="Tipo de Crescimento" selected="true"/>
			<property name="Form Normal" selected="true"/>
			<property name="Normalizado Adequadamente" selected="true"/>
		</entity_table>
		<attribute_column>
			<property name="Nome" selected="true"/>
			<property name="Tipo de Dados" selected="true"/>
			<property name="Espécie de Tipo de Dados" selected="true"/>
			<property name="Obrigatório" selected="true"/>
			<property name="Valor Default" selected="true"/>
			<property name="Nome da Restrição de Verificação" selected="true"/>
			<property name="Usar Restrição de Domínio" selected="true"/>
			<property name="Restrição de Verificação" selected="true"/>
			<property name="Restrição de Faixa" selected="true"/>
			<property name="Restrição LOV" selected="true"/>
			<property name="Comentário" selected="true"/>
			<property name="Comentário no RDBMS" selected="true"/>
			<property name="Observações" selected="true"/>
			<property name="Tipo de Origem" selected="true"/>
			<property name="Descrição da Fórmula" selected="true"/>
			<property name="Substituição de Tipo" selected="true"/>
			<property name="Escopo" selected="true"/>
		</attribute_column>
		<key_index>
			<property name="Nome" selected="true"/>
			<property name="Comentário" selected="true"/>
			<property name="Comentário no RDBMS" selected="true"/>
			<property name="Observações" selected="true"/>
			<property name="Chave Primária" selected="true"/>
			<property name="Atributos/Colunas" selected="true"/>
		</key_index>
		<relation_fk>
			<property name="Nome" selected="true"/>
			<property name="Deletar Regra" selected="true"/>
			<property name="Comentário" selected="true"/>
			<property name="Comentário no RDBMS" selected="true"/>
			<property name="Observações" selected="true"/>
		</relation_fk>
		<entityview_view>
			<property name="Nome" selected="true"/>
			<property name="Comentário" selected="true"/>
			<property name="Comentário no RDBMS" selected="true"/>
			<property name="Observações" selected="true"/>
			<property name="Tipo Estruturado" selected="true"/>
			<property name="WHERE" selected="true"/>
			<property name="HAVING" selected="true"/>
			<property name="Definido pelo Usuário" selected="true"/>
		</entityview_view>
	</eng_compare>
	<model_compare_props_filter>
	</model_compare_props_filter>
	<model_compare_storage_props_filter>
	</model_compare_storage_props_filter>
	<naming_options>
		<model_options objectid="1A7BE70A-5F39-26E8-467D-382A961AD165">
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.Table" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.Column" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.TableView" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.constraint.TableLevelConstraint" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.FKIndexAssociation" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.Index" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
		</model_options>
		<model_options objectid="79A12F87-8FAE-BC77-E578-E66A2C51177E">
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.Table" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.Column" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.TableView" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.constraint.TableLevelConstraint" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.FKIndexAssociation" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.relational.Index" max_name_length="30" case_type="2" valid_characters="" all_valid="true" />
		</model_options>
		<model_options objectid="52101419-16F4-9E6B-EE57-5F9E86F2C37B">
			<naming_option class_name="oracle.dbtools.crest.model.design.logical.Entity" max_name_length="254" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.logical.Attribute" max_name_length="254" case_type="2" valid_characters="" all_valid="true" />
			<naming_option class_name="oracle.dbtools.crest.model.design.logical.EntityView" max_name_length="254" case_type="2" valid_characters="" all_valid="true" />
		</model_options>
	</naming_options>
	<merge_conflicts>
	</merge_conflicts>
	<deleted_files>
	</deleted_files>
</settings>