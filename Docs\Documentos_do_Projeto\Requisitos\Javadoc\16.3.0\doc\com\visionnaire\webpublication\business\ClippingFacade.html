<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:21 BRT 2013 -->
<title>ClippingFacade</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="ClippingFacade";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ClippingFacade.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev Class</li>
<li><a href="../../../../com/visionnaire/webpublication/business/ComponentFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/ClippingFacade.html" target="_top">Frames</a></li>
<li><a href="ClippingFacade.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business</div>
<h2 title="Class ClippingFacade" class="title">Class ClippingFacade</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>ClippingFacade</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="strong">ClippingFacade</span>
extends Object</pre>
<dl><dt><span class="strong">Author:</span></dt>
  <dd>danilo</dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private static <a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html" title="class in com.visionnaire.webpublication.business">ClippingFacade</a></code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html#instance">instance</a></strong></code>
<div class="block">instancia de ClippingFacade</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html#ClippingFacade()">ClippingFacade</a></strong>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html#criaLink(long)">criaLink</a></strong>(long&nbsp;numPage)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html#criaPaginaClipping(java.lang.String, long, java.lang.String, java.io.PrintWriter)">criaPaginaClipping</a></strong>(String&nbsp;xmlClipping,
                  long&nbsp;sitePid,
                  String&nbsp;xsl,
                  PrintWriter&nbsp;out)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html" title="class in com.visionnaire.webpublication.business">ClippingFacade</a></code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html#getInstance()">getInstance</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html#getNumerosdePagina(int, int)">getNumerosdePagina</a></strong>(int&nbsp;numberOfContents,
                  int&nbsp;nrItensPorPagina)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html#realizaPaginacao(int, int, int)">realizaPaginacao</a></strong>(int&nbsp;totalPages,
                int&nbsp;atualPage,
                int&nbsp;nrItensPorPagina)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html#searchClipping(java.lang.String, int, java.lang.String)">searchClipping</a></strong>(String&nbsp;pageNumber,
              int&nbsp;results_per_page,
              String&nbsp;enderecoXML)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="instance">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>instance</h4>
<pre>private static&nbsp;<a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html" title="class in com.visionnaire.webpublication.business">ClippingFacade</a> instance</pre>
<div class="block">instancia de ClippingFacade</div>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ClippingFacade()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ClippingFacade</h4>
<pre>public&nbsp;ClippingFacade()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html" title="class in com.visionnaire.webpublication.business">ClippingFacade</a>&nbsp;getInstance()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>ClippingFacade</dd></dl>
</li>
</ul>
<a name="searchClipping(java.lang.String, int, java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>searchClipping</h4>
<pre>public&nbsp;String&nbsp;searchClipping(String&nbsp;pageNumber,
                    int&nbsp;results_per_page,
                    String&nbsp;enderecoXML)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>pageNumber</code> - </dd><dd><code>results_per_page</code> - </dd><dd><code>enderecoXML</code> - </dd>
<dt><span class="strong">Returns:</span></dt><dd>String xml do site passado</dd></dl>
</li>
</ul>
<a name="criaPaginaClipping(java.lang.String, long, java.lang.String, java.io.PrintWriter)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>criaPaginaClipping</h4>
<pre>public&nbsp;void&nbsp;criaPaginaClipping(String&nbsp;xmlClipping,
                      long&nbsp;sitePid,
                      String&nbsp;xsl,
                      PrintWriter&nbsp;out)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>xmlClipping</code> - </dd><dd><code>sitePid</code> - </dd><dd><code>xsl</code> - </dd><dd><code>out</code> - </dd></dl>
</li>
</ul>
<a name="realizaPaginacao(int, int, int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>realizaPaginacao</h4>
<pre>private&nbsp;String&nbsp;realizaPaginacao(int&nbsp;totalPages,
                      int&nbsp;atualPage,
                      int&nbsp;nrItensPorPagina)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>totalPages</code> - </dd><dd><code>atualPage</code> - </dd><dd><code>nrItensPorPagina</code> - </dd>
<dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="criaLink(long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>criaLink</h4>
<pre>private&nbsp;String&nbsp;criaLink(long&nbsp;numPage)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>numPage</code> - </dd>
<dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="getNumerosdePagina(int, int)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getNumerosdePagina</h4>
<pre>private&nbsp;int&nbsp;getNumerosdePagina(int&nbsp;numberOfContents,
                     int&nbsp;nrItensPorPagina)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>numberOfContents</code> - </dd><dd><code>nrItensPorPagina</code> - </dd>
<dt><span class="strong">Returns:</span></dt><dd>int</dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ClippingFacade.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev Class</li>
<li><a href="../../../../com/visionnaire/webpublication/business/ComponentFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/ClippingFacade.html" target="_top">Frames</a></li>
<li><a href="ClippingFacade.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
