<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:24 BRT 2013 -->
<title>PServantVisualContentImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantVisualContentImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantVisualContentImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantUploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li>Next Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html" target="_top">Frames</a></li>
<li><a href="PServantVisualContentImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent.pss</div>
<h2 title="Class PServantVisualContentImpl" class="title">Class PServantVisualContentImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantVisualContentImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantVisualContentImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#allOriginalContent__q">allOriginalContent__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#allowCommentaries__v">allowCommentaries__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexos__q">anexos__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexosImage__q">anexosImage__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private char</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#approved__v">approved__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#author__k">author__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#author__o">author__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private UserImpl</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#author__v">author__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#bodyField__v">bodyField__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#branch__q">branch__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#childrenContents__q">childrenContents__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#commentaries__q">commentaries__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SenderContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SenderContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#commentarySenders__q">commentarySenders__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#component__k">component__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#component__o">component__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#component__v">component__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentAuthorEmail__v">contentAuthorEmail__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentAuthorName__v">contentAuthorName__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentDescription__v">contentDescription__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentLinkIt__q">contentLinkIt__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentShortUrl__v">contentShortUrl__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentUrl__v">contentUrl__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#creationDate__v">creationDate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#editionDate__v">editionDate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#emailResponsavel__v">emailResponsavel__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#fatherContentPid__k">fatherContentPid__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#fatherContentPid__o">fatherContentPid__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#fatherContentPid__v">fatherContentPid__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#files__q">files__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#firstEdition__v">firstEdition__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#flowEntity__k">flowEntity__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#flowEntity__o">flowEntity__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/workflow/FlowEntityImpl.html" title="class in com.visionnaire.webpublication.workflow">FlowEntityImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#flowEntity__v">flowEntity__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontFile__k">frontFile__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontFile__o">frontFile__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontFile__v">frontFile__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontImage__k">frontImage__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontImage__o">frontImage__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontImage__v">frontImage__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#gerararquivodetalhe__v">gerararquivodetalhe__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#groupName__k">groupName__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#groupName__o">groupName__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#groupName__v">groupName__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#hat__v">hat__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/HistoryImpl.html" title="class in com.visionnaire.webpublication.business">HistoryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#history__q">history__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#images__q">images__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#internalTitle__v">internalTitle__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/NewsErrorRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">NewsErrorRegistryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#newsErrorRegistry__q">newsErrorRegistry__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">NewsFriendRegistryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#newsFriendRegistry__q">newsFriendRegistry__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#newWindow__v">newWindow__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#originalContent__o">originalContent__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#originalContent__v">originalContent__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#preModeracao__v">preModeracao__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#preview__v">preview__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#priority__v">priority__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#publishedDate__v">publishedDate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#relatedLink__q">relatedLink__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#removeDate__v">removeDate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#removedDate__v">removedDate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#serencontradobusca__v">serencontradobusca__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#tags__q">tags__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#toShow__v">toShow__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#urlContentPid__v">urlContentPid__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#urlExternal__v">urlExternal__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#wiki__v">wiki__v</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#PServantVisualContentImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantVisualContentImpl</a></strong>(Persistent&nbsp;jobj,
                         pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#PServantVisualContentImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantVisualContentImpl</a></strong>(pid&nbsp;id,
                         Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#allOriginalContent()">allOriginalContent</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#allOriginalContentCount()">allOriginalContentCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#allowCommentaries()">allowCommentaries</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#allowCommentaries(boolean)">allowCommentaries</a></strong>(boolean&nbsp;allowCommentaries)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexos()">anexos</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexos(com.visionnaire.PSS.client.PList)">anexos</a></strong>(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexosCount()">anexosCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexosImage()">anexosImage</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexosImage(com.visionnaire.PSS.client.PList)">anexosImage</a></strong>(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexosImageCount()">anexosImageCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexosImageInsert(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">anexosImageInsert</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;image)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexosImageRemove(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">anexosImageRemove</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;image)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexosInsert(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">anexosInsert</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;anexos)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#anexosRemove(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">anexosRemove</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;anexos)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#approved()">approved</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#approved(char)">approved</a></strong>(char&nbsp;approved)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>UserImpl</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#author()">author</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#author(com.visionnaire.webpublication.access.UserImpl)">author</a></strong>(UserImpl&nbsp;author)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#bodyField()">bodyField</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#bodyField(java.lang.String)">bodyField</a></strong>(String&nbsp;bodyField)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#branch()">branch</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#branchCount()">branchCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#childrenContents()">childrenContents</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#childrenContentsCount()">childrenContentsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#commentaries()">commentaries</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#commentariesCount()">commentariesCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SenderContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SenderContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#commentarySenders()">commentarySenders</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#commentarySendersCount()">commentarySendersCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#component()">component</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#component(com.visionnaire.webpublication.business.ComponentImpl)">component</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;component)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentAuthorEmail()">contentAuthorEmail</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentAuthorEmail(java.lang.String)">contentAuthorEmail</a></strong>(String&nbsp;contentAuthorEmail)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentAuthorName()">contentAuthorName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentAuthorName(java.lang.String)">contentAuthorName</a></strong>(String&nbsp;contentAuthorName)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentDescription()">contentDescription</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentDescription(java.lang.String)">contentDescription</a></strong>(String&nbsp;contentDescription)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentLinkIt()">contentLinkIt</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentLinkIt(com.visionnaire.PSS.client.PList)">contentLinkIt</a></strong>(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentLinkItCount()">contentLinkItCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentLinkItInsert(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">contentLinkItInsert</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;content)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentLinkItRemove(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">contentLinkItRemove</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;content)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentShortUrl()">contentShortUrl</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentShortUrl(java.lang.String)">contentShortUrl</a></strong>(String&nbsp;contentShortUrl)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentUrl()">contentUrl</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#contentUrl(java.lang.String)">contentUrl</a></strong>(String&nbsp;contentUrl)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#creationDate()">creationDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#creationDate(java.util.Date)">creationDate</a></strong>(Date&nbsp;creationDate)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#editionDate()">editionDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#editionDate(java.util.Date)">editionDate</a></strong>(Date&nbsp;editionDate)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#emailResponsavel()">emailResponsavel</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#emailResponsavel(java.lang.String)">emailResponsavel</a></strong>(String&nbsp;emailResponsavel)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#fatherContentPid()">fatherContentPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#fatherContentPid(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">fatherContentPid</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;fatherContentPid)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#files()">files</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#files(com.visionnaire.PSS.client.PList)">files</a></strong>(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#filesCount()">filesCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#filesInsert(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">filesInsert</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#filesRemove(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">filesRemove</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#firstEdition()">firstEdition</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#firstEdition(java.util.Date)">firstEdition</a></strong>(Date&nbsp;firstEdition)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/workflow/FlowEntityImpl.html" title="class in com.visionnaire.webpublication.workflow">FlowEntityImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#flowEntity()">flowEntity</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#flowEntity(com.visionnaire.webpublication.workflow.FlowEntityImpl)">flowEntity</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/workflow/FlowEntityImpl.html" title="class in com.visionnaire.webpublication.workflow">FlowEntityImpl</a>&nbsp;flowEntity)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontFile()">frontFile</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontFile(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">frontFile</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;frontFile)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontImage()">frontImage</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#frontImage(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">frontImage</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;frontImage)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#gerararquivodetalhe()">gerararquivodetalhe</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#gerararquivodetalhe(boolean)">gerararquivodetalhe</a></strong>(boolean&nbsp;gerararquivodetalhe)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#groupName()">groupName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#groupName(com.visionnaire.webpublication.business.visualcontent.GroupNameImpl)">groupName</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a>&nbsp;groupName)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#hat()">hat</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#hat(java.lang.String)">hat</a></strong>(String&nbsp;hat)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/HistoryImpl.html" title="class in com.visionnaire.webpublication.business">HistoryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#history()">history</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#historyCount()">historyCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#images()">images</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#images(com.visionnaire.PSS.client.PList)">images</a></strong>(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#imagesCount()">imagesCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#imagesInsert(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">imagesInsert</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;image)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#imagesRemove(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">imagesRemove</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;image)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#internalTitle()">internalTitle</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#internalTitle(java.lang.String)">internalTitle</a></strong>(String&nbsp;internalTitle)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/NewsErrorRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">NewsErrorRegistryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#newsErrorRegistry()">newsErrorRegistry</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#newsErrorRegistryCount()">newsErrorRegistryCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">NewsFriendRegistryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#newsFriendRegistry()">newsFriendRegistry</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#newsFriendRegistryCount()">newsFriendRegistryCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#newWindow()">newWindow</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#newWindow(boolean)">newWindow</a></strong>(boolean&nbsp;newWindow)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#originalContent()">originalContent</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#originalContent(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">originalContent</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;originalContent)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#preModeracao()">preModeracao</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#preModeracao(boolean)">preModeracao</a></strong>(boolean&nbsp;preModeracao)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#preview()">preview</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#preview(boolean)">preview</a></strong>(boolean&nbsp;preview)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#priority()">priority</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#priority(int)">priority</a></strong>(int&nbsp;priority)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#publishedDate()">publishedDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#publishedDate(java.util.Date)">publishedDate</a></strong>(Date&nbsp;publishedDate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#relatedLink()">relatedLink</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#relatedLink(com.visionnaire.PSS.client.PList)">relatedLink</a></strong>(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#relatedLinkCount()">relatedLinkCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#relatedLinkInsert(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">relatedLinkInsert</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;related)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#relatedLinkRemove(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">relatedLinkRemove</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;related)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#removeDate()">removeDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#removeDate(java.util.Date)">removeDate</a></strong>(Date&nbsp;removeDate)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#removedDate()">removedDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#removedDate(java.util.Date)">removedDate</a></strong>(Date&nbsp;removedDate)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#serencontradobusca()">serencontradobusca</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#serencontradobusca(boolean)">serencontradobusca</a></strong>(boolean&nbsp;serencontradobusca)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#tags()">tags</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#tags(com.visionnaire.PSS.client.PList)">tags</a></strong>(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#tagsCount()">tagsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#tagsInsert(com.visionnaire.webpublication.business.visualcontent.TagImpl)">tagsInsert</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&nbsp;tag)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#tagsRemove(com.visionnaire.webpublication.business.visualcontent.TagImpl)">tagsRemove</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&nbsp;tag)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#toShow()">toShow</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#toShow(boolean)">toShow</a></strong>(boolean&nbsp;toShow)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#urlContentPid()">urlContentPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#urlContentPid(java.lang.String)">urlContentPid</a></strong>(String&nbsp;urlContentPid)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#urlExternal()">urlExternal</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#urlExternal(int)">urlExternal</a></strong>(int&nbsp;urlExternal)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#wiki()">wiki</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html#wiki(boolean)">wiki</a></strong>(boolean&nbsp;wiki)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="editionDate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editionDate__v</h4>
<pre>private&nbsp;Date editionDate__v</pre>
</li>
</ul>
<a name="firstEdition__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>firstEdition__v</h4>
<pre>private&nbsp;Date firstEdition__v</pre>
</li>
</ul>
<a name="removeDate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeDate__v</h4>
<pre>private&nbsp;Date removeDate__v</pre>
</li>
</ul>
<a name="removedDate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removedDate__v</h4>
<pre>private&nbsp;Date removedDate__v</pre>
</li>
</ul>
<a name="publishedDate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>publishedDate__v</h4>
<pre>private&nbsp;Date publishedDate__v</pre>
</li>
</ul>
<a name="priority__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>priority__v</h4>
<pre>private&nbsp;int priority__v</pre>
</li>
</ul>
<a name="approved__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>approved__v</h4>
<pre>private&nbsp;char approved__v</pre>
</li>
</ul>
<a name="creationDate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>creationDate__v</h4>
<pre>private&nbsp;Date creationDate__v</pre>
</li>
</ul>
<a name="contentUrl__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentUrl__v</h4>
<pre>private&nbsp;String contentUrl__v</pre>
</li>
</ul>
<a name="urlExternal__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>urlExternal__v</h4>
<pre>private&nbsp;int urlExternal__v</pre>
</li>
</ul>
<a name="newWindow__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newWindow__v</h4>
<pre>private&nbsp;boolean newWindow__v</pre>
</li>
</ul>
<a name="toShow__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toShow__v</h4>
<pre>private&nbsp;boolean toShow__v</pre>
</li>
</ul>
<a name="emailResponsavel__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emailResponsavel__v</h4>
<pre>private&nbsp;String emailResponsavel__v</pre>
</li>
</ul>
<a name="bodyField__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bodyField__v</h4>
<pre>private&nbsp;String bodyField__v</pre>
</li>
</ul>
<a name="internalTitle__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>internalTitle__v</h4>
<pre>private&nbsp;String internalTitle__v</pre>
</li>
</ul>
<a name="urlContentPid__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>urlContentPid__v</h4>
<pre>private&nbsp;String urlContentPid__v</pre>
</li>
</ul>
<a name="contentDescription__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentDescription__v</h4>
<pre>private&nbsp;String contentDescription__v</pre>
</li>
</ul>
<a name="hat__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hat__v</h4>
<pre>private&nbsp;String hat__v</pre>
</li>
</ul>
<a name="contentAuthorName__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentAuthorName__v</h4>
<pre>private&nbsp;String contentAuthorName__v</pre>
</li>
</ul>
<a name="contentAuthorEmail__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentAuthorEmail__v</h4>
<pre>private&nbsp;String contentAuthorEmail__v</pre>
</li>
</ul>
<a name="preview__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>preview__v</h4>
<pre>private&nbsp;boolean preview__v</pre>
</li>
</ul>
<a name="wiki__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wiki__v</h4>
<pre>private&nbsp;boolean wiki__v</pre>
</li>
</ul>
<a name="allowCommentaries__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allowCommentaries__v</h4>
<pre>private&nbsp;boolean allowCommentaries__v</pre>
</li>
</ul>
<a name="preModeracao__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>preModeracao__v</h4>
<pre>private&nbsp;boolean preModeracao__v</pre>
</li>
</ul>
<a name="gerararquivodetalhe__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerararquivodetalhe__v</h4>
<pre>private&nbsp;boolean gerararquivodetalhe__v</pre>
</li>
</ul>
<a name="serencontradobusca__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serencontradobusca__v</h4>
<pre>private&nbsp;boolean serencontradobusca__v</pre>
</li>
</ul>
<a name="contentShortUrl__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentShortUrl__v</h4>
<pre>private&nbsp;String contentShortUrl__v</pre>
</li>
</ul>
<a name="author__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>author__o</h4>
<pre>private&nbsp;pid author__o</pre>
</li>
</ul>
<a name="author__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>author__k</h4>
<pre>private&nbsp;pid author__k</pre>
</li>
</ul>
<a name="author__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>author__v</h4>
<pre>private&nbsp;UserImpl author__v</pre>
</li>
</ul>
<a name="component__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component__o</h4>
<pre>private&nbsp;pid component__o</pre>
</li>
</ul>
<a name="component__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component__k</h4>
<pre>private&nbsp;pid component__k</pre>
</li>
</ul>
<a name="component__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a> component__v</pre>
</li>
</ul>
<a name="flowEntity__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flowEntity__o</h4>
<pre>private&nbsp;pid flowEntity__o</pre>
</li>
</ul>
<a name="flowEntity__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flowEntity__k</h4>
<pre>private&nbsp;pid flowEntity__k</pre>
</li>
</ul>
<a name="flowEntity__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flowEntity__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/workflow/FlowEntityImpl.html" title="class in com.visionnaire.webpublication.workflow">FlowEntityImpl</a> flowEntity__v</pre>
</li>
</ul>
<a name="history__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>history__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/HistoryImpl.html" title="class in com.visionnaire.webpublication.business">HistoryImpl</a>&gt; history__q</pre>
</li>
</ul>
<a name="groupName__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupName__o</h4>
<pre>private&nbsp;pid groupName__o</pre>
</li>
</ul>
<a name="groupName__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupName__k</h4>
<pre>private&nbsp;pid groupName__k</pre>
</li>
</ul>
<a name="groupName__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupName__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a> groupName__v</pre>
</li>
</ul>
<a name="frontFile__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontFile__o</h4>
<pre>private&nbsp;pid frontFile__o</pre>
</li>
</ul>
<a name="frontFile__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontFile__k</h4>
<pre>private&nbsp;pid frontFile__k</pre>
</li>
</ul>
<a name="frontFile__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontFile__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a> frontFile__v</pre>
</li>
</ul>
<a name="frontImage__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontImage__o</h4>
<pre>private&nbsp;pid frontImage__o</pre>
</li>
</ul>
<a name="frontImage__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontImage__k</h4>
<pre>private&nbsp;pid frontImage__k</pre>
</li>
</ul>
<a name="frontImage__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontImage__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a> frontImage__v</pre>
</li>
</ul>
<a name="images__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>images__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt; images__q</pre>
</li>
</ul>
<a name="tags__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tags__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&gt; tags__q</pre>
</li>
</ul>
<a name="files__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>files__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt; files__q</pre>
</li>
</ul>
<a name="anexos__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexos__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt; anexos__q</pre>
</li>
</ul>
<a name="anexosImage__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosImage__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt; anexosImage__q</pre>
</li>
</ul>
<a name="commentaries__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentaries__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt; commentaries__q</pre>
</li>
</ul>
<a name="branch__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>branch__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt; branch__q</pre>
</li>
</ul>
<a name="childrenContents__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>childrenContents__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt; childrenContents__q</pre>
</li>
</ul>
<a name="fatherContentPid__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherContentPid__o</h4>
<pre>private&nbsp;pid fatherContentPid__o</pre>
</li>
</ul>
<a name="fatherContentPid__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherContentPid__k</h4>
<pre>private&nbsp;pid fatherContentPid__k</pre>
</li>
</ul>
<a name="fatherContentPid__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherContentPid__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a> fatherContentPid__v</pre>
</li>
</ul>
<a name="relatedLink__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>relatedLink__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt; relatedLink__q</pre>
</li>
</ul>
<a name="contentLinkIt__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentLinkIt__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt; contentLinkIt__q</pre>
</li>
</ul>
<a name="allOriginalContent__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allOriginalContent__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt; allOriginalContent__q</pre>
</li>
</ul>
<a name="originalContent__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>originalContent__o</h4>
<pre>private&nbsp;pid originalContent__o</pre>
</li>
</ul>
<a name="originalContent__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>originalContent__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a> originalContent__v</pre>
</li>
</ul>
<a name="commentarySenders__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentarySenders__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SenderContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SenderContentImpl</a>&gt; commentarySenders__q</pre>
</li>
</ul>
<a name="newsFriendRegistry__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newsFriendRegistry__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">NewsFriendRegistryImpl</a>&gt; newsFriendRegistry__q</pre>
</li>
</ul>
<a name="newsErrorRegistry__q">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>newsErrorRegistry__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/NewsErrorRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">NewsErrorRegistryImpl</a>&gt; newsErrorRegistry__q</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantVisualContentImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantVisualContentImpl</h4>
<pre>public&nbsp;PServantVisualContentImpl(pid&nbsp;id,
                         Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantVisualContentImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantVisualContentImpl</h4>
<pre>public&nbsp;PServantVisualContentImpl(Persistent&nbsp;jobj,
                         pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="editionDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editionDate</h4>
<pre>public final&nbsp;Date&nbsp;editionDate()</pre>
</li>
</ul>
<a name="editionDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editionDate</h4>
<pre>public final&nbsp;void&nbsp;editionDate(Date&nbsp;editionDate)</pre>
</li>
</ul>
<a name="firstEdition()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>firstEdition</h4>
<pre>public final&nbsp;Date&nbsp;firstEdition()</pre>
</li>
</ul>
<a name="firstEdition(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>firstEdition</h4>
<pre>public final&nbsp;void&nbsp;firstEdition(Date&nbsp;firstEdition)</pre>
</li>
</ul>
<a name="removeDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeDate</h4>
<pre>public final&nbsp;Date&nbsp;removeDate()</pre>
</li>
</ul>
<a name="removeDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeDate</h4>
<pre>public final&nbsp;void&nbsp;removeDate(Date&nbsp;removeDate)</pre>
</li>
</ul>
<a name="removedDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removedDate</h4>
<pre>public final&nbsp;Date&nbsp;removedDate()</pre>
</li>
</ul>
<a name="removedDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removedDate</h4>
<pre>public final&nbsp;void&nbsp;removedDate(Date&nbsp;removedDate)</pre>
</li>
</ul>
<a name="publishedDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>publishedDate</h4>
<pre>public final&nbsp;Date&nbsp;publishedDate()</pre>
</li>
</ul>
<a name="publishedDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>publishedDate</h4>
<pre>public final&nbsp;void&nbsp;publishedDate(Date&nbsp;publishedDate)</pre>
</li>
</ul>
<a name="priority()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>priority</h4>
<pre>public final&nbsp;int&nbsp;priority()</pre>
</li>
</ul>
<a name="priority(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>priority</h4>
<pre>public final&nbsp;void&nbsp;priority(int&nbsp;priority)</pre>
</li>
</ul>
<a name="approved()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>approved</h4>
<pre>public final&nbsp;char&nbsp;approved()</pre>
</li>
</ul>
<a name="approved(char)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>approved</h4>
<pre>public final&nbsp;void&nbsp;approved(char&nbsp;approved)</pre>
</li>
</ul>
<a name="creationDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>creationDate</h4>
<pre>public final&nbsp;Date&nbsp;creationDate()</pre>
</li>
</ul>
<a name="creationDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>creationDate</h4>
<pre>public final&nbsp;void&nbsp;creationDate(Date&nbsp;creationDate)</pre>
</li>
</ul>
<a name="contentUrl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentUrl</h4>
<pre>public final&nbsp;String&nbsp;contentUrl()</pre>
</li>
</ul>
<a name="contentUrl(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentUrl</h4>
<pre>public final&nbsp;void&nbsp;contentUrl(String&nbsp;contentUrl)</pre>
</li>
</ul>
<a name="urlExternal()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>urlExternal</h4>
<pre>public final&nbsp;int&nbsp;urlExternal()</pre>
</li>
</ul>
<a name="urlExternal(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>urlExternal</h4>
<pre>public final&nbsp;void&nbsp;urlExternal(int&nbsp;urlExternal)</pre>
</li>
</ul>
<a name="newWindow()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newWindow</h4>
<pre>public final&nbsp;boolean&nbsp;newWindow()</pre>
</li>
</ul>
<a name="newWindow(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newWindow</h4>
<pre>public final&nbsp;void&nbsp;newWindow(boolean&nbsp;newWindow)</pre>
</li>
</ul>
<a name="toShow()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toShow</h4>
<pre>public final&nbsp;boolean&nbsp;toShow()</pre>
</li>
</ul>
<a name="toShow(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toShow</h4>
<pre>public final&nbsp;void&nbsp;toShow(boolean&nbsp;toShow)</pre>
</li>
</ul>
<a name="emailResponsavel()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emailResponsavel</h4>
<pre>public final&nbsp;String&nbsp;emailResponsavel()</pre>
</li>
</ul>
<a name="emailResponsavel(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emailResponsavel</h4>
<pre>public final&nbsp;void&nbsp;emailResponsavel(String&nbsp;emailResponsavel)</pre>
</li>
</ul>
<a name="bodyField()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bodyField</h4>
<pre>public final&nbsp;String&nbsp;bodyField()</pre>
</li>
</ul>
<a name="bodyField(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bodyField</h4>
<pre>public final&nbsp;void&nbsp;bodyField(String&nbsp;bodyField)</pre>
</li>
</ul>
<a name="internalTitle()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>internalTitle</h4>
<pre>public final&nbsp;String&nbsp;internalTitle()</pre>
</li>
</ul>
<a name="internalTitle(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>internalTitle</h4>
<pre>public final&nbsp;void&nbsp;internalTitle(String&nbsp;internalTitle)</pre>
</li>
</ul>
<a name="urlContentPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>urlContentPid</h4>
<pre>public final&nbsp;String&nbsp;urlContentPid()</pre>
</li>
</ul>
<a name="urlContentPid(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>urlContentPid</h4>
<pre>public final&nbsp;void&nbsp;urlContentPid(String&nbsp;urlContentPid)</pre>
</li>
</ul>
<a name="contentDescription()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentDescription</h4>
<pre>public final&nbsp;String&nbsp;contentDescription()</pre>
</li>
</ul>
<a name="contentDescription(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentDescription</h4>
<pre>public final&nbsp;void&nbsp;contentDescription(String&nbsp;contentDescription)</pre>
</li>
</ul>
<a name="hat()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hat</h4>
<pre>public final&nbsp;String&nbsp;hat()</pre>
</li>
</ul>
<a name="hat(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hat</h4>
<pre>public final&nbsp;void&nbsp;hat(String&nbsp;hat)</pre>
</li>
</ul>
<a name="contentAuthorName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentAuthorName</h4>
<pre>public final&nbsp;String&nbsp;contentAuthorName()</pre>
</li>
</ul>
<a name="contentAuthorName(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentAuthorName</h4>
<pre>public final&nbsp;void&nbsp;contentAuthorName(String&nbsp;contentAuthorName)</pre>
</li>
</ul>
<a name="contentAuthorEmail()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentAuthorEmail</h4>
<pre>public final&nbsp;String&nbsp;contentAuthorEmail()</pre>
</li>
</ul>
<a name="contentAuthorEmail(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentAuthorEmail</h4>
<pre>public final&nbsp;void&nbsp;contentAuthorEmail(String&nbsp;contentAuthorEmail)</pre>
</li>
</ul>
<a name="preview()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>preview</h4>
<pre>public final&nbsp;boolean&nbsp;preview()</pre>
</li>
</ul>
<a name="preview(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>preview</h4>
<pre>public final&nbsp;void&nbsp;preview(boolean&nbsp;preview)</pre>
</li>
</ul>
<a name="wiki()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wiki</h4>
<pre>public final&nbsp;boolean&nbsp;wiki()</pre>
</li>
</ul>
<a name="wiki(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wiki</h4>
<pre>public final&nbsp;void&nbsp;wiki(boolean&nbsp;wiki)</pre>
</li>
</ul>
<a name="allowCommentaries()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allowCommentaries</h4>
<pre>public final&nbsp;boolean&nbsp;allowCommentaries()</pre>
</li>
</ul>
<a name="allowCommentaries(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allowCommentaries</h4>
<pre>public final&nbsp;void&nbsp;allowCommentaries(boolean&nbsp;allowCommentaries)</pre>
</li>
</ul>
<a name="preModeracao()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>preModeracao</h4>
<pre>public final&nbsp;boolean&nbsp;preModeracao()</pre>
</li>
</ul>
<a name="preModeracao(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>preModeracao</h4>
<pre>public final&nbsp;void&nbsp;preModeracao(boolean&nbsp;preModeracao)</pre>
</li>
</ul>
<a name="gerararquivodetalhe()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerararquivodetalhe</h4>
<pre>public final&nbsp;boolean&nbsp;gerararquivodetalhe()</pre>
</li>
</ul>
<a name="gerararquivodetalhe(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerararquivodetalhe</h4>
<pre>public final&nbsp;void&nbsp;gerararquivodetalhe(boolean&nbsp;gerararquivodetalhe)</pre>
</li>
</ul>
<a name="serencontradobusca()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serencontradobusca</h4>
<pre>public final&nbsp;boolean&nbsp;serencontradobusca()</pre>
</li>
</ul>
<a name="serencontradobusca(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serencontradobusca</h4>
<pre>public final&nbsp;void&nbsp;serencontradobusca(boolean&nbsp;serencontradobusca)</pre>
</li>
</ul>
<a name="contentShortUrl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentShortUrl</h4>
<pre>public final&nbsp;String&nbsp;contentShortUrl()</pre>
</li>
</ul>
<a name="contentShortUrl(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentShortUrl</h4>
<pre>public final&nbsp;void&nbsp;contentShortUrl(String&nbsp;contentShortUrl)</pre>
</li>
</ul>
<a name="author()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>author</h4>
<pre>public final&nbsp;UserImpl&nbsp;author()</pre>
</li>
</ul>
<a name="author(com.visionnaire.webpublication.access.UserImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>author</h4>
<pre>public final&nbsp;void&nbsp;author(UserImpl&nbsp;author)</pre>
</li>
</ul>
<a name="component()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;component()</pre>
</li>
</ul>
<a name="component(com.visionnaire.webpublication.business.ComponentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component</h4>
<pre>public final&nbsp;void&nbsp;component(<a href="../../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;component)</pre>
</li>
</ul>
<a name="flowEntity()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flowEntity</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/workflow/FlowEntityImpl.html" title="class in com.visionnaire.webpublication.workflow">FlowEntityImpl</a>&nbsp;flowEntity()</pre>
</li>
</ul>
<a name="flowEntity(com.visionnaire.webpublication.workflow.FlowEntityImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flowEntity</h4>
<pre>public final&nbsp;void&nbsp;flowEntity(<a href="../../../../../../com/visionnaire/webpublication/workflow/FlowEntityImpl.html" title="class in com.visionnaire.webpublication.workflow">FlowEntityImpl</a>&nbsp;flowEntity)</pre>
</li>
</ul>
<a name="history()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>history</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/HistoryImpl.html" title="class in com.visionnaire.webpublication.business">HistoryImpl</a>&gt;&nbsp;history()</pre>
</li>
</ul>
<a name="historyCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>historyCount</h4>
<pre>public final&nbsp;int&nbsp;historyCount()</pre>
</li>
</ul>
<a name="groupName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupName</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a>&nbsp;groupName()</pre>
</li>
</ul>
<a name="groupName(com.visionnaire.webpublication.business.visualcontent.GroupNameImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupName</h4>
<pre>public final&nbsp;void&nbsp;groupName(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a>&nbsp;groupName)</pre>
</li>
</ul>
<a name="frontFile()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontFile</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;frontFile()</pre>
</li>
</ul>
<a name="frontFile(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontFile</h4>
<pre>public final&nbsp;void&nbsp;frontFile(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;frontFile)</pre>
</li>
</ul>
<a name="frontImage()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontImage</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;frontImage()</pre>
</li>
</ul>
<a name="frontImage(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontImage</h4>
<pre>public final&nbsp;void&nbsp;frontImage(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;frontImage)</pre>
</li>
</ul>
<a name="images()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>images</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;&nbsp;images()</pre>
</li>
</ul>
<a name="imagesCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imagesCount</h4>
<pre>public final&nbsp;int&nbsp;imagesCount()</pre>
</li>
</ul>
<a name="imagesInsert(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imagesInsert</h4>
<pre>public final&nbsp;void&nbsp;imagesInsert(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;image)</pre>
</li>
</ul>
<a name="imagesRemove(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imagesRemove</h4>
<pre>public final&nbsp;void&nbsp;imagesRemove(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;image)</pre>
</li>
</ul>
<a name="images(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>images</h4>
<pre>public final&nbsp;void&nbsp;images(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="tags()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tags</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&gt;&nbsp;tags()</pre>
</li>
</ul>
<a name="tagsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tagsCount</h4>
<pre>public final&nbsp;int&nbsp;tagsCount()</pre>
</li>
</ul>
<a name="tagsInsert(com.visionnaire.webpublication.business.visualcontent.TagImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tagsInsert</h4>
<pre>public final&nbsp;void&nbsp;tagsInsert(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&nbsp;tag)</pre>
</li>
</ul>
<a name="tagsRemove(com.visionnaire.webpublication.business.visualcontent.TagImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tagsRemove</h4>
<pre>public final&nbsp;void&nbsp;tagsRemove(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&nbsp;tag)</pre>
</li>
</ul>
<a name="tags(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tags</h4>
<pre>public final&nbsp;void&nbsp;tags(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">TagImpl</a>&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="files()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>files</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;&nbsp;files()</pre>
</li>
</ul>
<a name="filesCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesCount</h4>
<pre>public final&nbsp;int&nbsp;filesCount()</pre>
</li>
</ul>
<a name="filesInsert(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesInsert</h4>
<pre>public final&nbsp;void&nbsp;filesInsert(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;file)</pre>
</li>
</ul>
<a name="filesRemove(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesRemove</h4>
<pre>public final&nbsp;void&nbsp;filesRemove(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;file)</pre>
</li>
</ul>
<a name="files(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>files</h4>
<pre>public final&nbsp;void&nbsp;files(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="anexos()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexos</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;&nbsp;anexos()</pre>
</li>
</ul>
<a name="anexosCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosCount</h4>
<pre>public final&nbsp;int&nbsp;anexosCount()</pre>
</li>
</ul>
<a name="anexosInsert(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosInsert</h4>
<pre>public final&nbsp;void&nbsp;anexosInsert(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;anexos)</pre>
</li>
</ul>
<a name="anexosRemove(com.visionnaire.webpublication.business.visualcontent.UploadedFileImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosRemove</h4>
<pre>public final&nbsp;void&nbsp;anexosRemove(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&nbsp;anexos)</pre>
</li>
</ul>
<a name="anexos(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexos</h4>
<pre>public final&nbsp;void&nbsp;anexos(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">UploadedFileImpl</a>&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="anexosImage()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosImage</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;&nbsp;anexosImage()</pre>
</li>
</ul>
<a name="anexosImageCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosImageCount</h4>
<pre>public final&nbsp;int&nbsp;anexosImageCount()</pre>
</li>
</ul>
<a name="anexosImageInsert(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosImageInsert</h4>
<pre>public final&nbsp;void&nbsp;anexosImageInsert(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;image)</pre>
</li>
</ul>
<a name="anexosImageRemove(com.visionnaire.webpublication.business.visualcontent.ImageImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosImageRemove</h4>
<pre>public final&nbsp;void&nbsp;anexosImageRemove(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&nbsp;image)</pre>
</li>
</ul>
<a name="anexosImage(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosImage</h4>
<pre>public final&nbsp;void&nbsp;anexosImage(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ImageImpl</a>&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="commentaries()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentaries</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt;&nbsp;commentaries()</pre>
</li>
</ul>
<a name="commentariesCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentariesCount</h4>
<pre>public final&nbsp;int&nbsp;commentariesCount()</pre>
</li>
</ul>
<a name="branch()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>branch</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;branch()</pre>
</li>
</ul>
<a name="branchCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>branchCount</h4>
<pre>public final&nbsp;int&nbsp;branchCount()</pre>
</li>
</ul>
<a name="childrenContents()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>childrenContents</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;childrenContents()</pre>
</li>
</ul>
<a name="childrenContentsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>childrenContentsCount</h4>
<pre>public final&nbsp;int&nbsp;childrenContentsCount()</pre>
</li>
</ul>
<a name="fatherContentPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherContentPid</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;fatherContentPid()</pre>
</li>
</ul>
<a name="fatherContentPid(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherContentPid</h4>
<pre>public final&nbsp;void&nbsp;fatherContentPid(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;fatherContentPid)</pre>
</li>
</ul>
<a name="relatedLink()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>relatedLink</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;relatedLink()</pre>
</li>
</ul>
<a name="relatedLinkCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>relatedLinkCount</h4>
<pre>public final&nbsp;int&nbsp;relatedLinkCount()</pre>
</li>
</ul>
<a name="relatedLinkInsert(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>relatedLinkInsert</h4>
<pre>public final&nbsp;void&nbsp;relatedLinkInsert(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;related)</pre>
</li>
</ul>
<a name="relatedLinkRemove(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>relatedLinkRemove</h4>
<pre>public final&nbsp;void&nbsp;relatedLinkRemove(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;related)</pre>
</li>
</ul>
<a name="relatedLink(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>relatedLink</h4>
<pre>public final&nbsp;void&nbsp;relatedLink(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="contentLinkIt()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentLinkIt</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;contentLinkIt()</pre>
</li>
</ul>
<a name="contentLinkItCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentLinkItCount</h4>
<pre>public final&nbsp;int&nbsp;contentLinkItCount()</pre>
</li>
</ul>
<a name="contentLinkItInsert(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentLinkItInsert</h4>
<pre>public final&nbsp;void&nbsp;contentLinkItInsert(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;content)</pre>
</li>
</ul>
<a name="contentLinkItRemove(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentLinkItRemove</h4>
<pre>public final&nbsp;void&nbsp;contentLinkItRemove(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;content)</pre>
</li>
</ul>
<a name="contentLinkIt(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentLinkIt</h4>
<pre>public final&nbsp;void&nbsp;contentLinkIt(PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="allOriginalContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allOriginalContent</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;allOriginalContent()</pre>
</li>
</ul>
<a name="allOriginalContentCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allOriginalContentCount</h4>
<pre>public final&nbsp;int&nbsp;allOriginalContentCount()</pre>
</li>
</ul>
<a name="originalContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>originalContent</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;originalContent()</pre>
</li>
</ul>
<a name="originalContent(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>originalContent</h4>
<pre>public final&nbsp;void&nbsp;originalContent(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;originalContent)</pre>
</li>
</ul>
<a name="commentarySenders()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentarySenders</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SenderContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SenderContentImpl</a>&gt;&nbsp;commentarySenders()</pre>
</li>
</ul>
<a name="commentarySendersCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentarySendersCount</h4>
<pre>public final&nbsp;int&nbsp;commentarySendersCount()</pre>
</li>
</ul>
<a name="newsFriendRegistry()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newsFriendRegistry</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">NewsFriendRegistryImpl</a>&gt;&nbsp;newsFriendRegistry()</pre>
</li>
</ul>
<a name="newsFriendRegistryCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newsFriendRegistryCount</h4>
<pre>public final&nbsp;int&nbsp;newsFriendRegistryCount()</pre>
</li>
</ul>
<a name="newsErrorRegistry()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newsErrorRegistry</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/NewsErrorRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">NewsErrorRegistryImpl</a>&gt;&nbsp;newsErrorRegistry()</pre>
</li>
</ul>
<a name="newsErrorRegistryCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newsErrorRegistryCount</h4>
<pre>public final&nbsp;int&nbsp;newsErrorRegistryCount()</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantVisualContentImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantUploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li>Next Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantVisualContentImpl.html" target="_top">Frames</a></li>
<li><a href="PServantVisualContentImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
