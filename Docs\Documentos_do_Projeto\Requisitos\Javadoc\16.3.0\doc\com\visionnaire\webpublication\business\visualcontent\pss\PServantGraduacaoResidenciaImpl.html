<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:24 BRT 2013 -->
<title>PServantGraduacaoResidenciaImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantGraduacaoResidenciaImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantGraduacaoResidenciaImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantFreeComponentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html" target="_top">Frames</a></li>
<li><a href="PServantGraduacaoResidenciaImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent.pss</div>
<h2 title="Class PServantGraduacaoResidenciaImpl" class="title">Class PServantGraduacaoResidenciaImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantGraduacaoResidenciaImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantGraduacaoResidenciaImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#anoconclusao__v">anoconclusao__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#cidade__v">cidade__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#especialidadeCRM__k">especialidadeCRM__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#especialidadeCRM__o">especialidadeCRM__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#especialidadeCRM__v">especialidadeCRM__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#tipo__v">tipo__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#universidade__v">universidade__v</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#PServantGraduacaoResidenciaImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantGraduacaoResidenciaImpl</a></strong>(Persistent&nbsp;jobj,
                               pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#PServantGraduacaoResidenciaImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantGraduacaoResidenciaImpl</a></strong>(pid&nbsp;id,
                               Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#anoconclusao()">anoconclusao</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#anoconclusao(java.lang.String)">anoconclusao</a></strong>(String&nbsp;anoconclusao)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#cidade()">cidade</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#cidade(java.lang.String)">cidade</a></strong>(String&nbsp;cidade)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#especialidadeCRM()">especialidadeCRM</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#especialidadeCRM(com.visionnaire.webpublication.business.visualcontent.EspecialidadeCRMImpl)">especialidadeCRM</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a>&nbsp;especialidadeCRM)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#tipo()">tipo</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#tipo(java.lang.String)">tipo</a></strong>(String&nbsp;tipo)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#universidade()">universidade</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html#universidade(java.lang.String)">universidade</a></strong>(String&nbsp;universidade)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="tipo__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tipo__v</h4>
<pre>private&nbsp;String tipo__v</pre>
</li>
</ul>
<a name="universidade__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>universidade__v</h4>
<pre>private&nbsp;String universidade__v</pre>
</li>
</ul>
<a name="cidade__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cidade__v</h4>
<pre>private&nbsp;String cidade__v</pre>
</li>
</ul>
<a name="anoconclusao__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anoconclusao__v</h4>
<pre>private&nbsp;String anoconclusao__v</pre>
</li>
</ul>
<a name="especialidadeCRM__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>especialidadeCRM__o</h4>
<pre>private&nbsp;pid especialidadeCRM__o</pre>
</li>
</ul>
<a name="especialidadeCRM__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>especialidadeCRM__k</h4>
<pre>private&nbsp;pid especialidadeCRM__k</pre>
</li>
</ul>
<a name="especialidadeCRM__v">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>especialidadeCRM__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a> especialidadeCRM__v</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantGraduacaoResidenciaImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantGraduacaoResidenciaImpl</h4>
<pre>public&nbsp;PServantGraduacaoResidenciaImpl(pid&nbsp;id,
                               Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantGraduacaoResidenciaImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantGraduacaoResidenciaImpl</h4>
<pre>public&nbsp;PServantGraduacaoResidenciaImpl(Persistent&nbsp;jobj,
                               pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="tipo()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tipo</h4>
<pre>public final&nbsp;String&nbsp;tipo()</pre>
</li>
</ul>
<a name="tipo(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tipo</h4>
<pre>public final&nbsp;void&nbsp;tipo(String&nbsp;tipo)</pre>
</li>
</ul>
<a name="universidade()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>universidade</h4>
<pre>public final&nbsp;String&nbsp;universidade()</pre>
</li>
</ul>
<a name="universidade(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>universidade</h4>
<pre>public final&nbsp;void&nbsp;universidade(String&nbsp;universidade)</pre>
</li>
</ul>
<a name="cidade()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cidade</h4>
<pre>public final&nbsp;String&nbsp;cidade()</pre>
</li>
</ul>
<a name="cidade(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cidade</h4>
<pre>public final&nbsp;void&nbsp;cidade(String&nbsp;cidade)</pre>
</li>
</ul>
<a name="anoconclusao()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anoconclusao</h4>
<pre>public final&nbsp;String&nbsp;anoconclusao()</pre>
</li>
</ul>
<a name="anoconclusao(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anoconclusao</h4>
<pre>public final&nbsp;void&nbsp;anoconclusao(String&nbsp;anoconclusao)</pre>
</li>
</ul>
<a name="especialidadeCRM()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>especialidadeCRM</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a>&nbsp;especialidadeCRM()</pre>
</li>
</ul>
<a name="especialidadeCRM(com.visionnaire.webpublication.business.visualcontent.EspecialidadeCRMImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>especialidadeCRM</h4>
<pre>public final&nbsp;void&nbsp;especialidadeCRM(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a>&nbsp;especialidadeCRM)</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantGraduacaoResidenciaImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantFreeComponentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantGroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantGraduacaoResidenciaImpl.html" target="_top">Frames</a></li>
<li><a href="PServantGraduacaoResidenciaImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
