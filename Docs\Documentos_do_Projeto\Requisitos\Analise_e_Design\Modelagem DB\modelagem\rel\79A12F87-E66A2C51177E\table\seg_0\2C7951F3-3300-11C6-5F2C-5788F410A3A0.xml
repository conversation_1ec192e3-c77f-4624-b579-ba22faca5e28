<?xml version = '1.0' encoding = 'UTF-8'?>
<Table class="oracle.dbtools.crest.model.design.relational.Table" directorySegmentName="seg_0" id="2C7951F3-3300-11C6-5F2C-5788F410A3A0" name="webp_newslettermodel_comp">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<allowColumnReorder>false</allowColumnReorder>
<existDependencyGenerateInDDl>true</existDependencyGenerateInDDl>
<columns itemClass="oracle.dbtools.crest.model.design.relational.Column">
<Column name="pid" id="0B4F2397-F85A-3D09-679D-6588C8E0494C">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="klass" id="EBB2DFCC-4BF7-E0F2-7872-ABFEFB1A9BA7">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="newslettermodel" id="EE003FB2-BF09-9318-C703-6A6B757C261B">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<delegate>516A1F90-7937-95A3-BE8D-DABE74090DE1</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="6F9F39D0-6B4B-1764-770A-2B9B5A65B702" referredColumn="516A1F90-7937-95A3-BE8D-DABE74090DE1"/>
</associations>
</Column>
<Column name="newslettermodel_k" id="3211E538-0C74-AE76-5658-FE3B3B529BF8">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="component" id="ACC4A92F-30E5-3EA7-00AE-7B77DAA23E34">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<delegate>70CD13B8-F587-83D8-03FB-8F8A220EA88A</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="83B1B233-CCEE-23D1-C30E-6E5CAF89D731" referredColumn="70CD13B8-F587-83D8-03FB-8F8A220EA88A"/>
</associations>
</Column>
<Column name="component_k" id="6A4233CE-A76F-4335-5AC5-84F387A27BA0">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="ordernumber" id="9E937AE8-DBD6-DBC8-6C30-CBEE8617509A">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>C04303E2-3DBA-6647-D1F0-29CB523175A4</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="quantity" id="488AC38C-F2E6-E485-8FD5-B688D2E43A1E">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>C04303E2-3DBA-6647-D1F0-29CB523175A4</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
</columns>
<indexes itemClass="oracle.dbtools.crest.model.design.relational.Index">
<ind_PK_UK id="55748811-E93A-6CD1-CB73-4A20F225632E" name="webp_newslettermodel_comp_pk">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<pk>true</pk>
<indexState>Primary Constraint</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="0B4F2397-F85A-3D09-679D-6588C8E0494C"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="3043C16D-4FF3-DB69-F857-30CEE7E023DC" name="webp_newslettermodel_comp_fk">
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<indexState>Foreign Key</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="EE003FB2-BF09-9318-C703-6A6B757C261B"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="469A5206-6AC0-01B1-4DCE-8CAC37E05E63" name="webp_newslettermodel_comp_fk2">
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<indexState>Foreign Key</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="ACC4A92F-30E5-3EA7-00AE-7B77DAA23E34"/>
</indexColumnUsage>
</ind_PK_UK>
</indexes>
</Table>