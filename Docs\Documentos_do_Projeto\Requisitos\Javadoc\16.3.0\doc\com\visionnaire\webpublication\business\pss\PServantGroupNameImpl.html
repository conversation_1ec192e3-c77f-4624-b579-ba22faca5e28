<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:22 BRT 2013 -->
<title>PServantGroupNameImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantGroupNameImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantGroupNameImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantContentWorkFlowStateImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantHistoryImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html" target="_top">Frames</a></li>
<li><a href="PServantGroupNameImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.pss</div>
<h2 title="Class PServantGroupNameImpl" class="title">Class PServantGroupNameImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantGroupNameImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantGroupNameImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#component__k">component__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#component__o">component__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#component__v">component__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#contents__q">contents__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#description__v">description__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#groupCall__v">groupCall__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#groupPriority__v">groupPriority__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#PServantGroupNameImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantGroupNameImpl</a></strong>(Persistent&nbsp;jobj,
                     pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#PServantGroupNameImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantGroupNameImpl</a></strong>(pid&nbsp;id,
                     Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#component()">component</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#component(com.visionnaire.webpublication.business.ComponentImpl)">component</a></strong>(<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;component)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#contents()">contents</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#contentsCount()">contentsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#description()">description</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#description(java.lang.String)">description</a></strong>(String&nbsp;description)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#groupCall()">groupCall</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#groupCall(java.lang.String)">groupCall</a></strong>(String&nbsp;groupCall)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#groupPriority()">groupPriority</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#groupPriority(int)">groupPriority</a></strong>(int&nbsp;groupPriority)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="description__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>description__v</h4>
<pre>private&nbsp;String description__v</pre>
</li>
</ul>
<a name="groupPriority__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupPriority__v</h4>
<pre>private&nbsp;int groupPriority__v</pre>
</li>
</ul>
<a name="groupCall__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupCall__v</h4>
<pre>private&nbsp;String groupCall__v</pre>
</li>
</ul>
<a name="component__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component__o</h4>
<pre>private&nbsp;pid component__o</pre>
</li>
</ul>
<a name="component__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component__k</h4>
<pre>private&nbsp;pid component__k</pre>
</li>
</ul>
<a name="component__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component__v</h4>
<pre>private&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a> component__v</pre>
</li>
</ul>
<a name="contents__q">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>contents__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt; contents__q</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantGroupNameImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantGroupNameImpl</h4>
<pre>public&nbsp;PServantGroupNameImpl(pid&nbsp;id,
                     Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantGroupNameImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantGroupNameImpl</h4>
<pre>public&nbsp;PServantGroupNameImpl(Persistent&nbsp;jobj,
                     pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="description()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>description</h4>
<pre>public final&nbsp;String&nbsp;description()</pre>
</li>
</ul>
<a name="description(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>description</h4>
<pre>public final&nbsp;void&nbsp;description(String&nbsp;description)</pre>
</li>
</ul>
<a name="groupPriority()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupPriority</h4>
<pre>public final&nbsp;int&nbsp;groupPriority()</pre>
</li>
</ul>
<a name="groupPriority(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupPriority</h4>
<pre>public final&nbsp;void&nbsp;groupPriority(int&nbsp;groupPriority)</pre>
</li>
</ul>
<a name="groupCall()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupCall</h4>
<pre>public final&nbsp;String&nbsp;groupCall()</pre>
</li>
</ul>
<a name="groupCall(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupCall</h4>
<pre>public final&nbsp;void&nbsp;groupCall(String&nbsp;groupCall)</pre>
</li>
</ul>
<a name="component()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component</h4>
<pre>public final&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;component()</pre>
</li>
</ul>
<a name="component(com.visionnaire.webpublication.business.ComponentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component</h4>
<pre>public final&nbsp;void&nbsp;component(<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;component)</pre>
</li>
</ul>
<a name="contents()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contents</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;contents()</pre>
</li>
</ul>
<a name="contentsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentsCount</h4>
<pre>public final&nbsp;int&nbsp;contentsCount()</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantGroupNameImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantContentWorkFlowStateImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantHistoryImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/pss/PServantGroupNameImpl.html" target="_top">Frames</a></li>
<li><a href="PServantGroupNameImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
