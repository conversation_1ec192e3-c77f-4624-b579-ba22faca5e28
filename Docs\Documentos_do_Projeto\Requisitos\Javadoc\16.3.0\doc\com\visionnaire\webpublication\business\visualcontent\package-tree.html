<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:26 BRT 2013 -->
<title>com.visionnaire.webpublication.business.visualcontent Class Hierarchy</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="com.visionnaire.webpublication.business.visualcontent Class Hierarchy";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/package-tree.html">Prev</a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package com.visionnaire.webpublication.business.visualcontent</h1>
<span class="strong">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">Object
<ul>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CertifiedAuthentication.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">CertifiedAuthentication</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/FormacaoTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">FormacaoTransient</span></a> (implements Serializable)</li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">GraduacaoResidenciaTransient</span></a> (implements Serializable)</li>
<li type="circle">PSSUtil
<ul>
<li type="circle">Catalog
<ul>
<li type="circle">PersistentImplBase (implements Persistent)
<ul>
<li type="circle">BusinessObjectImpl
<ul>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/pss/BO.html" title="class in com.visionnaire.webpublication.pss"><span class="strong">BO</span></a>
<ul>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EmailTemplateImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">EmailTemplateImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">GroupNameImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/LogPoll.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">LogPoll</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/PollJustificationImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">PollJustificationImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchFileDataImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ResearchFileDataImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ResearchQuestionImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ResearchResponseImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">VisualContentImpl</span></a>
<ul>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/AnalistaImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">AnalistaImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/AuthenticationImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">AuthenticationImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ContactImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ContactImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ContatoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ContatoCRMImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/DicasImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">DicasImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeMedicoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">EspecialidadeMedicoCRMImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EventAgendaImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">EventAgendaImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/FaqImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">FaqImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/FormacaoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">FormacaoCRMImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/FreeComponentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">FreeComponentImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">NewsImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NotesImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">NotesImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/PollImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">PollImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ProductServiceImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ProductServiceImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ResearchImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSReaderImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">RSSReaderImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/SearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">SearchImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ThumbsImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ThumbsImpl</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">CommentaryImpl</span></a> (implements DependenceOnVisualContent)</li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">CommentarySenderImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Dicas_IntranetUserImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">EspecialidadeCRMImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">GraduacaoResidenciaImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ImageImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ImageImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsErrorRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">NewsErrorRegistryImpl</span></a> (implements DependenceOnVisualContent, NewsRegistrySender)</li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">NewsFriendRegistryImpl</span></a> (implements DependenceOnVisualContent, NewsRegistryDestinatary, NewsRegistrySender)</li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RelatedLinkImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">RelatedLinkImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">RSSItemImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/SenderContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">SenderContentImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">SubjectImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/TagGroupImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">TagGroupImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/TagImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">TagImpl</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/UploadedFileImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">UploadedFileImpl</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">QuestionarioPDFTransient</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">ResearchQuestionTransient</span></a> (implements Serializable)</li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">RespostaPDFTransient</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSFeedWarmer.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">RSSFeedWarmer</span></a></li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">RSSItemTransient</span></a> (implements Serializable)</li>
<li type="circle"><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/Search.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Search</span></a></li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/package-tree.html">Prev</a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
