<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:24 BRT 2013 -->
<title>PServantEventAgendaImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantEventAgendaImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantEventAgendaImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEspecialidadeMedicoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantFaqImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html" target="_top">Frames</a></li>
<li><a href="PServantEventAgendaImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent.pss</div>
<h2 title="Class PServantEventAgendaImpl" class="title">Class PServantEventAgendaImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantEventAgendaImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantEventAgendaImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#address__v">address__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#city__v">city__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#endDate__v">endDate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#eventDate__v">eventDate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#num__v">num__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research__k">research__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research__o">research__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research__v">research__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research2__k">research2__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research2__o">research2__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research2__v">research2__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research3__k">research3__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research3__o">research3__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research3__v">research3__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research4__k">research4__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research4__o">research4__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research4__v">research4__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#state__v">state__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#title__v">title__v</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#PServantEventAgendaImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantEventAgendaImpl</a></strong>(Persistent&nbsp;jobj,
                       pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#PServantEventAgendaImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantEventAgendaImpl</a></strong>(pid&nbsp;id,
                       Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#address()">address</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#address(java.lang.String)">address</a></strong>(String&nbsp;address)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#city()">city</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#city(java.lang.String)">city</a></strong>(String&nbsp;city)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#endDate()">endDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#endDate(java.util.Date)">endDate</a></strong>(Date&nbsp;endDate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#eventDate()">eventDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#eventDate(java.util.Date)">eventDate</a></strong>(Date&nbsp;eventDate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#num()">num</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#num(java.lang.String)">num</a></strong>(String&nbsp;num)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research()">research</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">research</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research2()">research2</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research2(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">research2</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research2)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research3()">research3</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research3(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">research3</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research3)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research4()">research4</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#research4(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">research4</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research4)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#state()">state</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#state(java.lang.String)">state</a></strong>(String&nbsp;state)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#title()">title</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html#title(java.lang.String)">title</a></strong>(String&nbsp;title)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="title__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>title__v</h4>
<pre>private&nbsp;String title__v</pre>
</li>
</ul>
<a name="eventDate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eventDate__v</h4>
<pre>private&nbsp;Date eventDate__v</pre>
</li>
</ul>
<a name="endDate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endDate__v</h4>
<pre>private&nbsp;Date endDate__v</pre>
</li>
</ul>
<a name="address__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>address__v</h4>
<pre>private&nbsp;String address__v</pre>
</li>
</ul>
<a name="num__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>num__v</h4>
<pre>private&nbsp;String num__v</pre>
</li>
</ul>
<a name="city__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>city__v</h4>
<pre>private&nbsp;String city__v</pre>
</li>
</ul>
<a name="state__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>state__v</h4>
<pre>private&nbsp;String state__v</pre>
</li>
</ul>
<a name="research__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research__o</h4>
<pre>private&nbsp;pid research__o</pre>
</li>
</ul>
<a name="research__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research__k</h4>
<pre>private&nbsp;pid research__k</pre>
</li>
</ul>
<a name="research__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a> research__v</pre>
</li>
</ul>
<a name="research2__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research2__o</h4>
<pre>private&nbsp;pid research2__o</pre>
</li>
</ul>
<a name="research2__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research2__k</h4>
<pre>private&nbsp;pid research2__k</pre>
</li>
</ul>
<a name="research2__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research2__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a> research2__v</pre>
</li>
</ul>
<a name="research3__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research3__o</h4>
<pre>private&nbsp;pid research3__o</pre>
</li>
</ul>
<a name="research3__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research3__k</h4>
<pre>private&nbsp;pid research3__k</pre>
</li>
</ul>
<a name="research3__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research3__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a> research3__v</pre>
</li>
</ul>
<a name="research4__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research4__o</h4>
<pre>private&nbsp;pid research4__o</pre>
</li>
</ul>
<a name="research4__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research4__k</h4>
<pre>private&nbsp;pid research4__k</pre>
</li>
</ul>
<a name="research4__v">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>research4__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a> research4__v</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantEventAgendaImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantEventAgendaImpl</h4>
<pre>public&nbsp;PServantEventAgendaImpl(pid&nbsp;id,
                       Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantEventAgendaImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantEventAgendaImpl</h4>
<pre>public&nbsp;PServantEventAgendaImpl(Persistent&nbsp;jobj,
                       pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="title()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>title</h4>
<pre>public final&nbsp;String&nbsp;title()</pre>
</li>
</ul>
<a name="title(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>title</h4>
<pre>public final&nbsp;void&nbsp;title(String&nbsp;title)</pre>
</li>
</ul>
<a name="eventDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eventDate</h4>
<pre>public final&nbsp;Date&nbsp;eventDate()</pre>
</li>
</ul>
<a name="eventDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eventDate</h4>
<pre>public final&nbsp;void&nbsp;eventDate(Date&nbsp;eventDate)</pre>
</li>
</ul>
<a name="endDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endDate</h4>
<pre>public final&nbsp;Date&nbsp;endDate()</pre>
</li>
</ul>
<a name="endDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endDate</h4>
<pre>public final&nbsp;void&nbsp;endDate(Date&nbsp;endDate)</pre>
</li>
</ul>
<a name="address()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>address</h4>
<pre>public final&nbsp;String&nbsp;address()</pre>
</li>
</ul>
<a name="address(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>address</h4>
<pre>public final&nbsp;void&nbsp;address(String&nbsp;address)</pre>
</li>
</ul>
<a name="num()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>num</h4>
<pre>public final&nbsp;String&nbsp;num()</pre>
</li>
</ul>
<a name="num(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>num</h4>
<pre>public final&nbsp;void&nbsp;num(String&nbsp;num)</pre>
</li>
</ul>
<a name="city()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>city</h4>
<pre>public final&nbsp;String&nbsp;city()</pre>
</li>
</ul>
<a name="city(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>city</h4>
<pre>public final&nbsp;void&nbsp;city(String&nbsp;city)</pre>
</li>
</ul>
<a name="state()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>state</h4>
<pre>public final&nbsp;String&nbsp;state()</pre>
</li>
</ul>
<a name="state(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>state</h4>
<pre>public final&nbsp;void&nbsp;state(String&nbsp;state)</pre>
</li>
</ul>
<a name="research()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research()</pre>
</li>
</ul>
<a name="research(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research</h4>
<pre>public final&nbsp;void&nbsp;research(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research)</pre>
</li>
</ul>
<a name="research2()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research2</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research2()</pre>
</li>
</ul>
<a name="research2(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research2</h4>
<pre>public final&nbsp;void&nbsp;research2(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research2)</pre>
</li>
</ul>
<a name="research3()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research3</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research3()</pre>
</li>
</ul>
<a name="research3(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research3</h4>
<pre>public final&nbsp;void&nbsp;research3(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research3)</pre>
</li>
</ul>
<a name="research4()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research4</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research4()</pre>
</li>
</ul>
<a name="research4(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research4</h4>
<pre>public final&nbsp;void&nbsp;research4(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research4)</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantEventAgendaImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantEspecialidadeMedicoCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantFaqImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantEventAgendaImpl.html" target="_top">Frames</a></li>
<li><a href="PServantEventAgendaImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
