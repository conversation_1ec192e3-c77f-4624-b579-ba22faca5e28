<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:23 BRT 2013 -->
<title>ResearchQuestionTransient</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="ResearchQuestionTransient";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResearchQuestionTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html" target="_top">Frames</a></li>
<li><a href="ResearchQuestionTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent</div>
<h2 title="Class ResearchQuestionTransient" class="title">Class ResearchQuestionTransient</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>ResearchQuestionTransient</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">ResearchQuestionTransient</span>
extends Object
implements Serializable</pre>
<dl><dt><span class="strong">Author:</span></dt>
  <dd>Fernando M. Santa Rosa</dd>
<dt><span class="strong">See Also:</span></dt><dd><a href="../../../../../serialized-form.html#com.visionnaire.webpublication.business.visualcontent.ResearchQuestionTransient">Serialized Form</a></dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#condition">condition</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#number">number</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#options">options</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#pid">pid</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#question">question</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#questionFatherNumber">questionFatherNumber</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#remove">remove</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#required">required</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#type">type</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#uniqueResponse">uniqueResponse</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#ResearchQuestionTransient(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String, boolean, boolean, java.lang.String, java.lang.String)">ResearchQuestionTransient</a></strong>(long&nbsp;_pid,
                         String&nbsp;_number,
                         String&nbsp;_question,
                         String&nbsp;_type,
                         String&nbsp;_options,
                         boolean&nbsp;_required,
                         boolean&nbsp;_uniqueResponse,
                         String&nbsp;_questionFatherNumber,
                         String&nbsp;_condition)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#getCondition()">getCondition</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#getNumber()">getNumber</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#getOptions()">getOptions</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#getPid()">getPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#getQuestion()">getQuestion</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#getQuestionFatherNumber()">getQuestionFatherNumber</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#getType()">getType</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#isRemove()">isRemove</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#isRequired()">isRequired</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#isUniqueResponse()">isUniqueResponse</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setCondition(java.lang.String)">setCondition</a></strong>(String&nbsp;condition)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setNumber(java.lang.String)">setNumber</a></strong>(String&nbsp;_number)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setOptions(java.lang.String)">setOptions</a></strong>(String&nbsp;_options)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setPid(long)">setPid</a></strong>(long&nbsp;_pid)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setQuestion(java.lang.String)">setQuestion</a></strong>(String&nbsp;_question)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setQuestionFatherNumber(java.lang.String)">setQuestionFatherNumber</a></strong>(String&nbsp;questionFatherNumber)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setRemove(boolean)">setRemove</a></strong>(boolean&nbsp;_remove)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setRequired(boolean)">setRequired</a></strong>(boolean&nbsp;_required)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setType(java.lang.String)">setType</a></strong>(String&nbsp;_type)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#setUniqueResponse(boolean)">setUniqueResponse</a></strong>(boolean&nbsp;uniqueResponse)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionTransient</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html#toTransient()">toTransient</a></strong>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="pid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pid</h4>
<pre>private&nbsp;long pid</pre>
</li>
</ul>
<a name="number">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>number</h4>
<pre>private&nbsp;String number</pre>
</li>
</ul>
<a name="question">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>question</h4>
<pre>private&nbsp;String question</pre>
</li>
</ul>
<a name="type">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>private&nbsp;String type</pre>
</li>
</ul>
<a name="options">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>options</h4>
<pre>private&nbsp;String options</pre>
</li>
</ul>
<a name="required">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>required</h4>
<pre>private&nbsp;boolean required</pre>
</li>
</ul>
<a name="uniqueResponse">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uniqueResponse</h4>
<pre>private&nbsp;boolean uniqueResponse</pre>
</li>
</ul>
<a name="remove">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>private&nbsp;boolean remove</pre>
</li>
</ul>
<a name="questionFatherNumber">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>questionFatherNumber</h4>
<pre>private&nbsp;String questionFatherNumber</pre>
</li>
</ul>
<a name="condition">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>condition</h4>
<pre>private&nbsp;String condition</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ResearchQuestionTransient(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String, boolean, boolean, java.lang.String, java.lang.String)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ResearchQuestionTransient</h4>
<pre>public&nbsp;ResearchQuestionTransient(long&nbsp;_pid,
                         String&nbsp;_number,
                         String&nbsp;_question,
                         String&nbsp;_type,
                         String&nbsp;_options,
                         boolean&nbsp;_required,
                         boolean&nbsp;_uniqueResponse,
                         String&nbsp;_questionFatherNumber,
                         String&nbsp;_condition)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_pid</code> - </dd><dd><code>_number</code> - </dd><dd><code>_question</code> - </dd><dd><code>_type</code> - </dd><dd><code>_options</code> - </dd><dd><code>_required</code> - </dd><dd><code>_uniqueResponse</code> - </dd><dd><code>_questionFatherNumber</code> - </dd><dd><code>_condition</code> - </dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPid</h4>
<pre>public&nbsp;long&nbsp;getPid()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>long</dd></dl>
</li>
</ul>
<a name="setPid(long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPid</h4>
<pre>public&nbsp;void&nbsp;setPid(long&nbsp;_pid)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_pid</code> - </dd></dl>
</li>
</ul>
<a name="getNumber()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumber</h4>
<pre>public&nbsp;String&nbsp;getNumber()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setNumber(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumber</h4>
<pre>public&nbsp;void&nbsp;setNumber(String&nbsp;_number)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_number</code> - </dd></dl>
</li>
</ul>
<a name="getOptions()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOptions</h4>
<pre>public&nbsp;String&nbsp;getOptions()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setOptions(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOptions</h4>
<pre>public&nbsp;void&nbsp;setOptions(String&nbsp;_options)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_options</code> - </dd></dl>
</li>
</ul>
<a name="getQuestion()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQuestion</h4>
<pre>public&nbsp;String&nbsp;getQuestion()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setQuestion(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuestion</h4>
<pre>public&nbsp;void&nbsp;setQuestion(String&nbsp;_question)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_question</code> - </dd></dl>
</li>
</ul>
<a name="isRequired()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRequired</h4>
<pre>public&nbsp;boolean&nbsp;isRequired()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>boolean</dd></dl>
</li>
</ul>
<a name="setRequired(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRequired</h4>
<pre>public&nbsp;void&nbsp;setRequired(boolean&nbsp;_required)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_required</code> - </dd></dl>
</li>
</ul>
<a name="getType()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;String&nbsp;getType()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setType(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(String&nbsp;_type)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_type</code> - </dd></dl>
</li>
</ul>
<a name="isRemove()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRemove</h4>
<pre>public&nbsp;boolean&nbsp;isRemove()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>boolean</dd></dl>
</li>
</ul>
<a name="setRemove(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemove</h4>
<pre>public&nbsp;void&nbsp;setRemove(boolean&nbsp;_remove)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_remove</code> - </dd></dl>
</li>
</ul>
<a name="toTransient()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toTransient</h4>
<pre>public&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionTransient</a>&nbsp;toTransient()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>ResearchQuestionTransient</dd></dl>
</li>
</ul>
<a name="getCondition()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCondition</h4>
<pre>public&nbsp;String&nbsp;getCondition()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>condition</dd></dl>
</li>
</ul>
<a name="setCondition(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCondition</h4>
<pre>public&nbsp;void&nbsp;setCondition(String&nbsp;condition)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>condition</code> - condition</dd></dl>
</li>
</ul>
<a name="getQuestionFatherNumber()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQuestionFatherNumber</h4>
<pre>public&nbsp;String&nbsp;getQuestionFatherNumber()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>questionFatherNumber</dd></dl>
</li>
</ul>
<a name="setQuestionFatherNumber(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuestionFatherNumber</h4>
<pre>public&nbsp;void&nbsp;setQuestionFatherNumber(String&nbsp;questionFatherNumber)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>questionFatherNumber</code> - questionFatherNumber</dd></dl>
</li>
</ul>
<a name="isUniqueResponse()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUniqueResponse</h4>
<pre>public&nbsp;boolean&nbsp;isUniqueResponse()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>boolean</dd></dl>
</li>
</ul>
<a name="setUniqueResponse(boolean)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setUniqueResponse</h4>
<pre>public&nbsp;void&nbsp;setUniqueResponse(boolean&nbsp;uniqueResponse)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>uniqueResponse</code> - </dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResearchQuestionTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html" target="_top">Frames</a></li>
<li><a href="ResearchQuestionTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
