<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:22 BRT 2013 -->
<title>PServantParameterComponentImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantParameterComponentImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantParameterComponentImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantNewsletterModelImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html" target="_top">Frames</a></li>
<li><a href="PServantParameterComponentImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.pss</div>
<h2 title="Class PServantParameterComponentImpl" class="title">Class PServantParameterComponentImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantParameterComponentImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantParameterComponentImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#addressDetails__v">addressDetails__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#addressNone__v">addressNone__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#addressUrl__v">addressUrl__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#component__k">component__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#component__o">component__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#component__v">component__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showAddress__v">showAddress__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showBodyField__v">showBodyField__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showComments__v">showComments__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showCredits__v">showCredits__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showExibitionOrder__v">showExibitionOrder__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showExportarXML__v">showExportarXML__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showFrontFile__v">showFrontFile__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showFrontImage__v">showFrontImage__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showGerarArquivos__v">showGerarArquivos__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showGroup__v">showGroup__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showHat__v">showHat__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showImages__v">showImages__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showRecommend__v">showRecommend__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showRegisterFiles__v">showRegisterFiles__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showRegisterImages__v">showRegisterImages__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showShortUrl__v">showShortUrl__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showTags__v">showTags__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showTie__v">showTie__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showUrl__v">showUrl__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showWorkflow__v">showWorkflow__v</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#PServantParameterComponentImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantParameterComponentImpl</a></strong>(Persistent&nbsp;jobj,
                              pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#PServantParameterComponentImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantParameterComponentImpl</a></strong>(pid&nbsp;id,
                              Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#addressDetails()">addressDetails</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#addressDetails(boolean)">addressDetails</a></strong>(boolean&nbsp;addressDetails)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#addressNone()">addressNone</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#addressNone(boolean)">addressNone</a></strong>(boolean&nbsp;addressNone)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#addressUrl()">addressUrl</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#addressUrl(boolean)">addressUrl</a></strong>(boolean&nbsp;addressUrl)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#component()">component</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#component(com.visionnaire.webpublication.business.ComponentImpl)">component</a></strong>(<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;component)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showAddress()">showAddress</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showAddress(boolean)">showAddress</a></strong>(boolean&nbsp;showAddress)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showBodyField()">showBodyField</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showBodyField(boolean)">showBodyField</a></strong>(boolean&nbsp;showBodyField)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showComments()">showComments</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showComments(boolean)">showComments</a></strong>(boolean&nbsp;showComments)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showCredits()">showCredits</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showCredits(boolean)">showCredits</a></strong>(boolean&nbsp;showCredits)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showExibitionOrder()">showExibitionOrder</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showExibitionOrder(boolean)">showExibitionOrder</a></strong>(boolean&nbsp;showExibitionOrder)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showExportarXML()">showExportarXML</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showExportarXML(boolean)">showExportarXML</a></strong>(boolean&nbsp;showExportarXML)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showFrontFile()">showFrontFile</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showFrontFile(boolean)">showFrontFile</a></strong>(boolean&nbsp;showFrontFile)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showFrontImage()">showFrontImage</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showFrontImage(boolean)">showFrontImage</a></strong>(boolean&nbsp;showFrontImage)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showGerarArquivos()">showGerarArquivos</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showGerarArquivos(boolean)">showGerarArquivos</a></strong>(boolean&nbsp;showGerarArquivos)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showGroup()">showGroup</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showGroup(boolean)">showGroup</a></strong>(boolean&nbsp;showGroup)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showHat()">showHat</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showHat(boolean)">showHat</a></strong>(boolean&nbsp;showHat)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showImages()">showImages</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showImages(boolean)">showImages</a></strong>(boolean&nbsp;showImages)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showRecommend()">showRecommend</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showRecommend(boolean)">showRecommend</a></strong>(boolean&nbsp;showRecommend)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showRegisterFiles()">showRegisterFiles</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showRegisterFiles(boolean)">showRegisterFiles</a></strong>(boolean&nbsp;showRegisterFiles)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showRegisterImages()">showRegisterImages</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showRegisterImages(boolean)">showRegisterImages</a></strong>(boolean&nbsp;showRegisterImages)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showShortUrl()">showShortUrl</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showShortUrl(boolean)">showShortUrl</a></strong>(boolean&nbsp;showShortUrl)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showTags()">showTags</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showTags(boolean)">showTags</a></strong>(boolean&nbsp;showTags)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showTie()">showTie</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showTie(boolean)">showTie</a></strong>(boolean&nbsp;showTie)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showUrl()">showUrl</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showUrl(boolean)">showUrl</a></strong>(boolean&nbsp;showUrl)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showWorkflow()">showWorkflow</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html#showWorkflow(boolean)">showWorkflow</a></strong>(boolean&nbsp;showWorkflow)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="showTie__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showTie__v</h4>
<pre>private&nbsp;boolean showTie__v</pre>
</li>
</ul>
<a name="showHat__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showHat__v</h4>
<pre>private&nbsp;boolean showHat__v</pre>
</li>
</ul>
<a name="showGroup__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showGroup__v</h4>
<pre>private&nbsp;boolean showGroup__v</pre>
</li>
</ul>
<a name="showUrl__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showUrl__v</h4>
<pre>private&nbsp;boolean showUrl__v</pre>
</li>
</ul>
<a name="showAddress__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showAddress__v</h4>
<pre>private&nbsp;boolean showAddress__v</pre>
</li>
</ul>
<a name="addressDetails__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addressDetails__v</h4>
<pre>private&nbsp;boolean addressDetails__v</pre>
</li>
</ul>
<a name="addressUrl__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addressUrl__v</h4>
<pre>private&nbsp;boolean addressUrl__v</pre>
</li>
</ul>
<a name="addressNone__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addressNone__v</h4>
<pre>private&nbsp;boolean addressNone__v</pre>
</li>
</ul>
<a name="showBodyField__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showBodyField__v</h4>
<pre>private&nbsp;boolean showBodyField__v</pre>
</li>
</ul>
<a name="showTags__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showTags__v</h4>
<pre>private&nbsp;boolean showTags__v</pre>
</li>
</ul>
<a name="showFrontImage__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showFrontImage__v</h4>
<pre>private&nbsp;boolean showFrontImage__v</pre>
</li>
</ul>
<a name="showFrontFile__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showFrontFile__v</h4>
<pre>private&nbsp;boolean showFrontFile__v</pre>
</li>
</ul>
<a name="showImages__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showImages__v</h4>
<pre>private&nbsp;boolean showImages__v</pre>
</li>
</ul>
<a name="showComments__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showComments__v</h4>
<pre>private&nbsp;boolean showComments__v</pre>
</li>
</ul>
<a name="showCredits__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showCredits__v</h4>
<pre>private&nbsp;boolean showCredits__v</pre>
</li>
</ul>
<a name="showRecommend__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showRecommend__v</h4>
<pre>private&nbsp;boolean showRecommend__v</pre>
</li>
</ul>
<a name="showWorkflow__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showWorkflow__v</h4>
<pre>private&nbsp;boolean showWorkflow__v</pre>
</li>
</ul>
<a name="showExibitionOrder__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExibitionOrder__v</h4>
<pre>private&nbsp;boolean showExibitionOrder__v</pre>
</li>
</ul>
<a name="showGerarArquivos__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showGerarArquivos__v</h4>
<pre>private&nbsp;boolean showGerarArquivos__v</pre>
</li>
</ul>
<a name="showExportarXML__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExportarXML__v</h4>
<pre>private&nbsp;boolean showExportarXML__v</pre>
</li>
</ul>
<a name="showShortUrl__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showShortUrl__v</h4>
<pre>private&nbsp;boolean showShortUrl__v</pre>
</li>
</ul>
<a name="showRegisterImages__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showRegisterImages__v</h4>
<pre>private&nbsp;boolean showRegisterImages__v</pre>
</li>
</ul>
<a name="showRegisterFiles__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showRegisterFiles__v</h4>
<pre>private&nbsp;boolean showRegisterFiles__v</pre>
</li>
</ul>
<a name="component__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component__o</h4>
<pre>private&nbsp;pid component__o</pre>
</li>
</ul>
<a name="component__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component__k</h4>
<pre>private&nbsp;pid component__k</pre>
</li>
</ul>
<a name="component__v">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>component__v</h4>
<pre>private&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a> component__v</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantParameterComponentImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantParameterComponentImpl</h4>
<pre>public&nbsp;PServantParameterComponentImpl(pid&nbsp;id,
                              Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantParameterComponentImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantParameterComponentImpl</h4>
<pre>public&nbsp;PServantParameterComponentImpl(Persistent&nbsp;jobj,
                              pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="showTie()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showTie</h4>
<pre>public final&nbsp;boolean&nbsp;showTie()</pre>
</li>
</ul>
<a name="showTie(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showTie</h4>
<pre>public final&nbsp;void&nbsp;showTie(boolean&nbsp;showTie)</pre>
</li>
</ul>
<a name="showHat()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showHat</h4>
<pre>public final&nbsp;boolean&nbsp;showHat()</pre>
</li>
</ul>
<a name="showHat(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showHat</h4>
<pre>public final&nbsp;void&nbsp;showHat(boolean&nbsp;showHat)</pre>
</li>
</ul>
<a name="showGroup()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showGroup</h4>
<pre>public final&nbsp;boolean&nbsp;showGroup()</pre>
</li>
</ul>
<a name="showGroup(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showGroup</h4>
<pre>public final&nbsp;void&nbsp;showGroup(boolean&nbsp;showGroup)</pre>
</li>
</ul>
<a name="showUrl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showUrl</h4>
<pre>public final&nbsp;boolean&nbsp;showUrl()</pre>
</li>
</ul>
<a name="showUrl(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showUrl</h4>
<pre>public final&nbsp;void&nbsp;showUrl(boolean&nbsp;showUrl)</pre>
</li>
</ul>
<a name="showAddress()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showAddress</h4>
<pre>public final&nbsp;boolean&nbsp;showAddress()</pre>
</li>
</ul>
<a name="showAddress(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showAddress</h4>
<pre>public final&nbsp;void&nbsp;showAddress(boolean&nbsp;showAddress)</pre>
</li>
</ul>
<a name="addressDetails()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addressDetails</h4>
<pre>public final&nbsp;boolean&nbsp;addressDetails()</pre>
</li>
</ul>
<a name="addressDetails(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addressDetails</h4>
<pre>public final&nbsp;void&nbsp;addressDetails(boolean&nbsp;addressDetails)</pre>
</li>
</ul>
<a name="addressUrl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addressUrl</h4>
<pre>public final&nbsp;boolean&nbsp;addressUrl()</pre>
</li>
</ul>
<a name="addressUrl(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addressUrl</h4>
<pre>public final&nbsp;void&nbsp;addressUrl(boolean&nbsp;addressUrl)</pre>
</li>
</ul>
<a name="addressNone()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addressNone</h4>
<pre>public final&nbsp;boolean&nbsp;addressNone()</pre>
</li>
</ul>
<a name="addressNone(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addressNone</h4>
<pre>public final&nbsp;void&nbsp;addressNone(boolean&nbsp;addressNone)</pre>
</li>
</ul>
<a name="showBodyField()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showBodyField</h4>
<pre>public final&nbsp;boolean&nbsp;showBodyField()</pre>
</li>
</ul>
<a name="showBodyField(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showBodyField</h4>
<pre>public final&nbsp;void&nbsp;showBodyField(boolean&nbsp;showBodyField)</pre>
</li>
</ul>
<a name="showTags()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showTags</h4>
<pre>public final&nbsp;boolean&nbsp;showTags()</pre>
</li>
</ul>
<a name="showTags(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showTags</h4>
<pre>public final&nbsp;void&nbsp;showTags(boolean&nbsp;showTags)</pre>
</li>
</ul>
<a name="showFrontImage()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showFrontImage</h4>
<pre>public final&nbsp;boolean&nbsp;showFrontImage()</pre>
</li>
</ul>
<a name="showFrontImage(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showFrontImage</h4>
<pre>public final&nbsp;void&nbsp;showFrontImage(boolean&nbsp;showFrontImage)</pre>
</li>
</ul>
<a name="showFrontFile()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showFrontFile</h4>
<pre>public final&nbsp;boolean&nbsp;showFrontFile()</pre>
</li>
</ul>
<a name="showFrontFile(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showFrontFile</h4>
<pre>public final&nbsp;void&nbsp;showFrontFile(boolean&nbsp;showFrontFile)</pre>
</li>
</ul>
<a name="showImages()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showImages</h4>
<pre>public final&nbsp;boolean&nbsp;showImages()</pre>
</li>
</ul>
<a name="showImages(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showImages</h4>
<pre>public final&nbsp;void&nbsp;showImages(boolean&nbsp;showImages)</pre>
</li>
</ul>
<a name="showComments()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showComments</h4>
<pre>public final&nbsp;boolean&nbsp;showComments()</pre>
</li>
</ul>
<a name="showComments(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showComments</h4>
<pre>public final&nbsp;void&nbsp;showComments(boolean&nbsp;showComments)</pre>
</li>
</ul>
<a name="showCredits()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showCredits</h4>
<pre>public final&nbsp;boolean&nbsp;showCredits()</pre>
</li>
</ul>
<a name="showCredits(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showCredits</h4>
<pre>public final&nbsp;void&nbsp;showCredits(boolean&nbsp;showCredits)</pre>
</li>
</ul>
<a name="showRecommend()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showRecommend</h4>
<pre>public final&nbsp;boolean&nbsp;showRecommend()</pre>
</li>
</ul>
<a name="showRecommend(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showRecommend</h4>
<pre>public final&nbsp;void&nbsp;showRecommend(boolean&nbsp;showRecommend)</pre>
</li>
</ul>
<a name="showWorkflow()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showWorkflow</h4>
<pre>public final&nbsp;boolean&nbsp;showWorkflow()</pre>
</li>
</ul>
<a name="showWorkflow(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showWorkflow</h4>
<pre>public final&nbsp;void&nbsp;showWorkflow(boolean&nbsp;showWorkflow)</pre>
</li>
</ul>
<a name="showExibitionOrder()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExibitionOrder</h4>
<pre>public final&nbsp;boolean&nbsp;showExibitionOrder()</pre>
</li>
</ul>
<a name="showExibitionOrder(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExibitionOrder</h4>
<pre>public final&nbsp;void&nbsp;showExibitionOrder(boolean&nbsp;showExibitionOrder)</pre>
</li>
</ul>
<a name="showGerarArquivos()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showGerarArquivos</h4>
<pre>public final&nbsp;boolean&nbsp;showGerarArquivos()</pre>
</li>
</ul>
<a name="showGerarArquivos(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showGerarArquivos</h4>
<pre>public final&nbsp;void&nbsp;showGerarArquivos(boolean&nbsp;showGerarArquivos)</pre>
</li>
</ul>
<a name="showExportarXML()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExportarXML</h4>
<pre>public final&nbsp;boolean&nbsp;showExportarXML()</pre>
</li>
</ul>
<a name="showExportarXML(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExportarXML</h4>
<pre>public final&nbsp;void&nbsp;showExportarXML(boolean&nbsp;showExportarXML)</pre>
</li>
</ul>
<a name="showShortUrl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showShortUrl</h4>
<pre>public final&nbsp;boolean&nbsp;showShortUrl()</pre>
</li>
</ul>
<a name="showShortUrl(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showShortUrl</h4>
<pre>public final&nbsp;void&nbsp;showShortUrl(boolean&nbsp;showShortUrl)</pre>
</li>
</ul>
<a name="showRegisterImages()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showRegisterImages</h4>
<pre>public final&nbsp;boolean&nbsp;showRegisterImages()</pre>
</li>
</ul>
<a name="showRegisterImages(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showRegisterImages</h4>
<pre>public final&nbsp;void&nbsp;showRegisterImages(boolean&nbsp;showRegisterImages)</pre>
</li>
</ul>
<a name="showRegisterFiles()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showRegisterFiles</h4>
<pre>public final&nbsp;boolean&nbsp;showRegisterFiles()</pre>
</li>
</ul>
<a name="showRegisterFiles(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showRegisterFiles</h4>
<pre>public final&nbsp;void&nbsp;showRegisterFiles(boolean&nbsp;showRegisterFiles)</pre>
</li>
</ul>
<a name="component()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component</h4>
<pre>public final&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;component()</pre>
</li>
</ul>
<a name="component(com.visionnaire.webpublication.business.ComponentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>component</h4>
<pre>public final&nbsp;void&nbsp;component(<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;component)</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantParameterComponentImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantNewsletterModelImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html" target="_top">Frames</a></li>
<li><a href="PServantParameterComponentImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
