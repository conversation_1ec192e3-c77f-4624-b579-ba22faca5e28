<?xml version = '1.0' encoding = 'UTF-8'?>
<Table class="oracle.dbtools.crest.model.design.relational.Table" directorySegmentName="seg_0" id="5F373540-7461-3525-C084-5AF46B2C2AEA" name="webp_espMedicoCRM_espCRM">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<allowColumnReorder>false</allowColumnReorder>
<existDependencyGenerateInDDl>true</existDependencyGenerateInDDl>
<columns itemClass="oracle.dbtools.crest.model.design.relational.Column">
<Column name="especialidadeCRM" id="5ED50E44-E6B1-DFB0-A122-CE155BA005E1">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<delegate>4F9D60F7-0E23-DB13-F58C-E7F67262FF0B</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="05B3DD49-A708-4DE3-D261-3531A2F8A4BE" referredColumn="4F9D60F7-0E23-DB13-F58C-E7F67262FF0B"/>
</associations>
</Column>
<Column name="especialidadeCRM_k" id="F97EDC58-C44A-15A2-313B-54C223535508">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="especialidadeMedicoCRM" id="8C104643-EA70-19C8-1859-53AB6F388967">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<delegate>090AF5B4-29DF-49F3-ED88-F61115BA97EA</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="CE5CAF7B-2129-DDAF-A086-9C1FA4C5E78D" referredColumn="090AF5B4-29DF-49F3-ED88-F61115BA97EA"/>
</associations>
</Column>
<Column name="especialidadeMedicoCRM_k" id="6A3EC6EE-ED50-CED2-CE54-1383A4E60162">
<sourceDDLFile>create_consolidado_oracle.sql</sourceDDLFile>
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<use>0</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<domain>0A61EE4B-BD5A-FA0E-286B-4C39A984A451</domain>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
</columns>
<indexes itemClass="oracle.dbtools.crest.model.design.relational.Index">
<ind_PK_UK id="06A5E1DE-F1C4-AF68-2980-26955685E6C1" name="webp_espMedicoCRM_espCRM_fk">
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<indexState>Foreign Key</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="8C104643-EA70-19C8-1859-53AB6F388967"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="BC0B3C1C-BA05-2A14-27C4-598692935DC0" name="webp_espMedicoCRM_espCRM_fk2">
<createdBy>andres</createdBy>
<createdTime>2013-05-14 17:54:54 UTC</createdTime>
<generatorID>Gerado pelo usuário</generatorID>
<ownerDesignName>modelagem</ownerDesignName>
<indexState>Foreign Key</indexState>
<isSurrogateKey>false</isSurrogateKey>
<indexColumnUsage>
<colUsage columnID="5ED50E44-E6B1-DFB0-A122-CE155BA005E1"/>
</indexColumnUsage>
</ind_PK_UK>
</indexes>
</Table>