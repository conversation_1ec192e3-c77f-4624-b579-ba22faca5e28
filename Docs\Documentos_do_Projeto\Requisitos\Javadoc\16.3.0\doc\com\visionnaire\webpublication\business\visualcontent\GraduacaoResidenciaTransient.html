<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:23 BRT 2013 -->
<title>GraduacaoResidenciaTransient</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="GraduacaoResidenciaTransient";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GraduacaoResidenciaTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html" target="_top">Frames</a></li>
<li><a href="GraduacaoResidenciaTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent</div>
<h2 title="Class GraduacaoResidenciaTransient" class="title">Class GraduacaoResidenciaTransient</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>GraduacaoResidenciaTransient</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">GraduacaoResidenciaTransient</span>
extends Object
implements Serializable</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../../serialized-form.html#com.visionnaire.webpublication.business.visualcontent.GraduacaoResidenciaTransient">Serialized Form</a></dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#anoConclusao">anoConclusao</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#cidade">cidade</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#especialidade">especialidade</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#id">id</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#tipo">tipo</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#universidade">universidade</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#GraduacaoResidenciaTransient(java.lang.String, java.lang.String, java.lang.String, java.lang.String, com.visionnaire.webpublication.business.visualcontent.EspecialidadeCRMImpl, int)">GraduacaoResidenciaTransient</a></strong>(String&nbsp;tipo,
                            String&nbsp;universidade,
                            String&nbsp;cidade,
                            String&nbsp;anoConclusao,
                            <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a>&nbsp;especialidade,
                            int&nbsp;id)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#getAnoConclusao()">getAnoConclusao</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#getCidade()">getCidade</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#getEspecialidade()">getEspecialidade</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#getId()">getId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#getTipo()">getTipo</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#getUniversidade()">getUniversidade</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#setAnoConclusao(java.lang.String)">setAnoConclusao</a></strong>(String&nbsp;anoConclusao)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#setCidade(java.lang.String)">setCidade</a></strong>(String&nbsp;cidade)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#setEspecialidade(com.visionnaire.webpublication.business.visualcontent.EspecialidadeCRMImpl)">setEspecialidade</a></strong>(<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a>&nbsp;especialidade)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#setId(int)">setId</a></strong>(int&nbsp;id)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#setTipo(java.lang.String)">setTipo</a></strong>(String&nbsp;tipo)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html#setUniversidade(java.lang.String)">setUniversidade</a></strong>(String&nbsp;universidade)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="id">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>id</h4>
<pre>private&nbsp;int id</pre>
</li>
</ul>
<a name="tipo">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tipo</h4>
<pre>private&nbsp;String tipo</pre>
</li>
</ul>
<a name="universidade">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>universidade</h4>
<pre>private&nbsp;String universidade</pre>
</li>
</ul>
<a name="cidade">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cidade</h4>
<pre>private&nbsp;String cidade</pre>
</li>
</ul>
<a name="anoConclusao">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anoConclusao</h4>
<pre>private&nbsp;String anoConclusao</pre>
</li>
</ul>
<a name="especialidade">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>especialidade</h4>
<pre>private&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a> especialidade</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="GraduacaoResidenciaTransient(java.lang.String, java.lang.String, java.lang.String, java.lang.String, com.visionnaire.webpublication.business.visualcontent.EspecialidadeCRMImpl, int)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GraduacaoResidenciaTransient</h4>
<pre>public&nbsp;GraduacaoResidenciaTransient(String&nbsp;tipo,
                            String&nbsp;universidade,
                            String&nbsp;cidade,
                            String&nbsp;anoConclusao,
                            <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a>&nbsp;especialidade,
                            int&nbsp;id)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTipo()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTipo</h4>
<pre>public&nbsp;String&nbsp;getTipo()</pre>
</li>
</ul>
<a name="setTipo(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTipo</h4>
<pre>public&nbsp;void&nbsp;setTipo(String&nbsp;tipo)</pre>
</li>
</ul>
<a name="getUniversidade()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniversidade</h4>
<pre>public&nbsp;String&nbsp;getUniversidade()</pre>
</li>
</ul>
<a name="setUniversidade(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUniversidade</h4>
<pre>public&nbsp;void&nbsp;setUniversidade(String&nbsp;universidade)</pre>
</li>
</ul>
<a name="getCidade()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCidade</h4>
<pre>public&nbsp;String&nbsp;getCidade()</pre>
</li>
</ul>
<a name="setCidade(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCidade</h4>
<pre>public&nbsp;void&nbsp;setCidade(String&nbsp;cidade)</pre>
</li>
</ul>
<a name="getAnoConclusao()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnoConclusao</h4>
<pre>public&nbsp;String&nbsp;getAnoConclusao()</pre>
</li>
</ul>
<a name="setAnoConclusao(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnoConclusao</h4>
<pre>public&nbsp;void&nbsp;setAnoConclusao(String&nbsp;anoConclusao)</pre>
</li>
</ul>
<a name="getEspecialidade()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEspecialidade</h4>
<pre>public&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a>&nbsp;getEspecialidade()</pre>
</li>
</ul>
<a name="setEspecialidade(com.visionnaire.webpublication.business.visualcontent.EspecialidadeCRMImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEspecialidade</h4>
<pre>public&nbsp;void&nbsp;setEspecialidade(<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/EspecialidadeCRMImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">EspecialidadeCRMImpl</a>&nbsp;especialidade)</pre>
</li>
</ul>
<a name="getId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getId</h4>
<pre>public&nbsp;int&nbsp;getId()</pre>
</li>
</ul>
<a name="setId(int)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setId</h4>
<pre>public&nbsp;void&nbsp;setId(int&nbsp;id)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GraduacaoResidenciaTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/GraduacaoResidenciaTransient.html" target="_top">Frames</a></li>
<li><a href="GraduacaoResidenciaTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
