<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:26 BRT 2013 -->
<title>Uses of Class com.visionnaire.webpublication.business.visualcontent.SubjectImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.visionnaire.webpublication.business.visualcontent.SubjectImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/\class-useSubjectImpl.html" target="_top">Frames</a></li>
<li><a href="SubjectImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.visionnaire.webpublication.business.visualcontent.SubjectImpl" class="title">Uses of Class<br>com.visionnaire.webpublication.business.visualcontent.SubjectImpl</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.visionnaire.webpublication.business">com.visionnaire.webpublication.business</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.visionnaire.webpublication.business.visualcontent">com.visionnaire.webpublication.business.visualcontent</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.visionnaire.webpublication.business.visualcontent.pss">com.visionnaire.webpublication.business.visualcontent.pss</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.visionnaire.webpublication.business">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a> in <a href="../../../../../../com/visionnaire/webpublication/business/package-summary.html">com.visionnaire.webpublication.business</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/package-summary.html">com.visionnaire.webpublication.business</a> that return <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></code></td>
<td class="colLast"><span class="strong">SubjectFacade.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/SubjectFacade.html#getSubjectRoot()">getSubjectRoot</a></strong>()</code>
<div class="block">Recupera o assunto raiz da arvore</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/package-summary.html">com.visionnaire.webpublication.business</a> that return types with arguments of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static List&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">SubjectFacade.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/SubjectFacade.html#getAllSubjectsList()">getAllSubjectsList</a></strong>()</code>
<div class="block">retorna todos os assuntos</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/package-summary.html">com.visionnaire.webpublication.business</a> with parameters of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>private static void</code></td>
<td class="colLast"><span class="strong">SubjectFacade.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/SubjectFacade.html#encarnateSubjectNodeIntoTreeViewToSelect(com.visionnaire.webpublication.business.visualcontent.SubjectImpl, com.visionnaire.webpublication.web.taglib.htmlfacilities.TreeViewTag, java.lang.String, boolean)">encarnateSubjectNodeIntoTreeViewToSelect</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&nbsp;subjectNode,
                                        <a href="../../../../../../com/visionnaire/webpublication/web/taglib/htmlfacilities/TreeViewTag.html" title="class in com.visionnaire.webpublication.web.taglib.htmlfacilities">TreeViewTag</a>&nbsp;root,
                                        String&nbsp;sitePid,
                                        boolean&nbsp;isRoot)</code>
<div class="block">Popula a treeview com os dados encarnados do BD.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.visionnaire.webpublication.business.visualcontent">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a> in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/package-summary.html">com.visionnaire.webpublication.business.visualcontent</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/package-summary.html">com.visionnaire.webpublication.business.visualcontent</a> that return <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></code></td>
<td class="colLast"><span class="strong">SubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html#getParent()">getParent</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></code></td>
<td class="colLast"><span class="strong">SubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html#getRoot()">getRoot</a></strong>()</code>
<div class="block">Retorna o Root dos assuntos.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></code></td>
<td class="colLast"><span class="strong">SubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html#restore(long)">restore</a></strong>(long&nbsp;pid)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/package-summary.html">com.visionnaire.webpublication.business.visualcontent</a> that return types with arguments of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>List&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">SubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html#getDescendants()">getDescendants</a></strong>()</code>
<div class="block">Retorna todos os descendentes deste assunto</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private static List&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">SubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html#getDescendants(com.visionnaire.webpublication.business.visualcontent.SubjectImpl)">getDescendants</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&nbsp;subject)</code>
<div class="block">Retorna assunto informado e seus descendentes recursivamente</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">SubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html#getSubjectChildren()">getSubjectChildren</a></strong>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/package-summary.html">com.visionnaire.webpublication.business.visualcontent</a> with parameters of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>private static List&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">SubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html#getDescendants(com.visionnaire.webpublication.business.visualcontent.SubjectImpl)">getDescendants</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&nbsp;subject)</code>
<div class="block">Retorna assunto informado e seus descendentes recursivamente</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html#setParent(com.visionnaire.webpublication.business.visualcontent.SubjectImpl)">setParent</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&nbsp;parent)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/package-summary.html">com.visionnaire.webpublication.business.visualcontent</a> with parameters of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html#SubjectImpl(java.lang.String, com.visionnaire.webpublication.business.visualcontent.SubjectImpl)">SubjectImpl</a></strong>(String&nbsp;description,
           <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&nbsp;parent)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.visionnaire.webpublication.business.visualcontent.pss">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a> in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-summary.html">com.visionnaire.webpublication.business.visualcontent.pss</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-summary.html">com.visionnaire.webpublication.business.visualcontent.pss</a> declared as <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></code></td>
<td class="colLast"><span class="strong">PServantSubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantSubjectImpl.html#parent__v">parent__v</a></strong></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-summary.html">com.visionnaire.webpublication.business.visualcontent.pss</a> with type parameters of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">PServantSubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantSubjectImpl.html#subjectChildren__q">subjectChildren__q</a></strong></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-summary.html">com.visionnaire.webpublication.business.visualcontent.pss</a> that return <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></code></td>
<td class="colLast"><span class="strong">PServantSubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantSubjectImpl.html#parent()">parent</a></strong>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-summary.html">com.visionnaire.webpublication.business.visualcontent.pss</a> that return types with arguments of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">PServantSubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantSubjectImpl.html#subjectChildren()">subjectChildren</a></strong>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-summary.html">com.visionnaire.webpublication.business.visualcontent.pss</a> with parameters of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">PServantSubjectImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantSubjectImpl.html#parent(com.visionnaire.webpublication.business.visualcontent.SubjectImpl)">parent</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SubjectImpl</a>&nbsp;parent)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/SubjectImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/\class-useSubjectImpl.html" target="_top">Frames</a></li>
<li><a href="SubjectImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
