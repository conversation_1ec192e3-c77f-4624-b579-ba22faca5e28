<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:22 BRT 2013 -->
<title>CommentarySenderImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="CommentarySenderImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CommentarySenderImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ContactImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html" target="_top">Frames</a></li>
<li><a href="CommentarySenderImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent</div>
<h2 title="Class CommentarySenderImpl" class="title">Class CommentarySenderImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>BusinessObjectImpl</li>
<li>
<ul class="inheritance">
<li>CommentarySenderImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">CommentarySenderImpl</span>
extends BusinessObjectImpl</pre>
<dl><dt><span class="strong">Author:</span></dt>
  <dd>fgbernardino</dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss">PServantCommentarySenderImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#ps">ps</a></strong></code>
<div class="block">ps</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#CommentarySenderImpl(com.visionnaire.PSS.pid)">CommentarySenderImpl</a></strong>(pid&nbsp;oid)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#CommentarySenderImpl(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, boolean)">CommentarySenderImpl</a></strong>(String&nbsp;name,
                    String&nbsp;email,
                    String&nbsp;siteAuthor,
                    String&nbsp;cityAuthor,
                    String&nbsp;stateAuthor,
                    String&nbsp;ipauthor,
                    String&nbsp;companyAuthor,
                    boolean&nbsp;receiveNews)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#createHtmlToSendEmail(int, java.lang.String, java.lang.String, java.lang.String, java.lang.String, com.visionnaire.webpublication.business.visualcontent.SenderContentImpl)">createHtmlToSendEmail</a></strong>(int&nbsp;numCommentaries,
                     String&nbsp;conteudo,
                     String&nbsp;site,
                     String&nbsp;data,
                     String&nbsp;url,
                     <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/SenderContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SenderContentImpl</a>&nbsp;senderContent)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getCityAuthor()">getCityAuthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getCommentaries()">getCommentaries</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getCompanyAuthor()">getCompanyAuthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getEmail()">getEmail</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getIpAuthor()">getIpAuthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getName()">getName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getReceiveNews()">getReceiveNews</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getSiteAuthor()">getSiteAuthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getStateAuthor()">getStateAuthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#getXmlSendEmail(int, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String)">getXmlSendEmail</a></strong>(int&nbsp;numCommentaries,
               String&nbsp;conteudo,
               String&nbsp;site,
               String&nbsp;data,
               String&nbsp;urlConteudo,
               String&nbsp;fileMore)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#pssDeleteFields()">pssDeleteFields</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#setCityAuthor(java.lang.String)">setCityAuthor</a></strong>(String&nbsp;cityAuthor)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#setCompanyAuthor(java.lang.String)">setCompanyAuthor</a></strong>(String&nbsp;companyAuthor)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#setEmail(java.lang.String)">setEmail</a></strong>(String&nbsp;email)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#setIpAuthor(java.lang.String)">setIpAuthor</a></strong>(String&nbsp;ipauthor)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#setName(java.lang.String)">setName</a></strong>(String&nbsp;name)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#setReceiveNews(boolean)">setReceiveNews</a></strong>(boolean&nbsp;receiveNews)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#setSiteAuthor(java.lang.String)">setSiteAuthor</a></strong>(String&nbsp;siteAuthor)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html#setStateAuthor(java.lang.String)">setStateAuthor</a></strong>(String&nbsp;stateAuthor)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_BusinessObjectImpl">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;BusinessObjectImpl</h3>
<code>getPid, pssServant, setPS</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, getClassId, hashCode, isDying, pssDelete, pssDestroy, pssLock, toInternal, toInternal, toString, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ps">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ps</h4>
<pre>private final&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss">PServantCommentarySenderImpl</a> ps</pre>
<div class="block">ps</div>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CommentarySenderImpl(com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CommentarySenderImpl</h4>
<pre>public&nbsp;CommentarySenderImpl(pid&nbsp;oid)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>oid</code> - </dd></dl>
</li>
</ul>
<a name="CommentarySenderImpl(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, boolean)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CommentarySenderImpl</h4>
<pre>public&nbsp;CommentarySenderImpl(String&nbsp;name,
                    String&nbsp;email,
                    String&nbsp;siteAuthor,
                    String&nbsp;cityAuthor,
                    String&nbsp;stateAuthor,
                    String&nbsp;ipauthor,
                    String&nbsp;companyAuthor,
                    boolean&nbsp;receiveNews)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>name</code> - </dd><dd><code>email</code> - </dd><dd><code>siteAuthor</code> - </dd><dd><code>cityAuthor</code> - </dd><dd><code>stateAuthor</code> - </dd><dd><code>ipauthor</code> - </dd><dd><code>companyAuthor</code> - </dd><dd><code>receiveNews</code> - </dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssDeleteFields()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssDeleteFields</h4>
<pre>public&nbsp;void&nbsp;pssDeleteFields()
                     throws NotDisconnectedException</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in class&nbsp;<code>PersistentImplBase</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>NotDisconnectedException</code></dd></dl>
</li>
</ul>
<a name="getCommentaries()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCommentaries</h4>
<pre>public&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt;&nbsp;getCommentaries()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>PList</dd></dl>
</li>
</ul>
<a name="getName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;String&nbsp;getName()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setName(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(String&nbsp;name)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>name</code> - </dd></dl>
</li>
</ul>
<a name="getEmail()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEmail</h4>
<pre>public&nbsp;String&nbsp;getEmail()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setEmail(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEmail</h4>
<pre>public&nbsp;void&nbsp;setEmail(String&nbsp;email)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>email</code> - </dd></dl>
</li>
</ul>
<a name="getSiteAuthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSiteAuthor</h4>
<pre>public&nbsp;String&nbsp;getSiteAuthor()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setSiteAuthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSiteAuthor</h4>
<pre>public&nbsp;void&nbsp;setSiteAuthor(String&nbsp;siteAuthor)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>siteAuthor</code> - </dd></dl>
</li>
</ul>
<a name="getCityAuthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCityAuthor</h4>
<pre>public&nbsp;String&nbsp;getCityAuthor()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setCityAuthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCityAuthor</h4>
<pre>public&nbsp;void&nbsp;setCityAuthor(String&nbsp;cityAuthor)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>cityAuthor</code> - </dd></dl>
</li>
</ul>
<a name="getStateAuthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStateAuthor</h4>
<pre>public&nbsp;String&nbsp;getStateAuthor()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setStateAuthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStateAuthor</h4>
<pre>public&nbsp;void&nbsp;setStateAuthor(String&nbsp;stateAuthor)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>stateAuthor</code> - </dd></dl>
</li>
</ul>
<a name="getIpAuthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIpAuthor</h4>
<pre>public&nbsp;String&nbsp;getIpAuthor()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setIpAuthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIpAuthor</h4>
<pre>public&nbsp;void&nbsp;setIpAuthor(String&nbsp;ipauthor)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>ipauthor</code> - </dd></dl>
</li>
</ul>
<a name="getCompanyAuthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompanyAuthor</h4>
<pre>public&nbsp;String&nbsp;getCompanyAuthor()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setCompanyAuthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompanyAuthor</h4>
<pre>public&nbsp;void&nbsp;setCompanyAuthor(String&nbsp;companyAuthor)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>companyAuthor</code> - </dd></dl>
</li>
</ul>
<a name="getReceiveNews()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReceiveNews</h4>
<pre>public&nbsp;boolean&nbsp;getReceiveNews()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>boolean</dd></dl>
</li>
</ul>
<a name="setReceiveNews(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReceiveNews</h4>
<pre>public&nbsp;void&nbsp;setReceiveNews(boolean&nbsp;receiveNews)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>receiveNews</code> - </dd></dl>
</li>
</ul>
<a name="createHtmlToSendEmail(int, java.lang.String, java.lang.String, java.lang.String, java.lang.String, com.visionnaire.webpublication.business.visualcontent.SenderContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHtmlToSendEmail</h4>
<pre>public&nbsp;String&nbsp;createHtmlToSendEmail(int&nbsp;numCommentaries,
                           String&nbsp;conteudo,
                           String&nbsp;site,
                           String&nbsp;data,
                           String&nbsp;url,
                           <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/SenderContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">SenderContentImpl</a>&nbsp;senderContent)
                             throws <a href="../../../../../com/visionnaire/webpublication/business/exception/BusinessException.html" title="class in com.visionnaire.webpublication.business.exception">BusinessException</a></pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>numCommentaries</code> - </dd><dd><code>conteudo</code> - </dd><dd><code>site</code> - </dd><dd><code>data</code> - </dd><dd><code>url</code> - </dd><dd><code>senderContent</code> - </dd>
<dt><span class="strong">Returns:</span></dt><dd>String</dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../../../com/visionnaire/webpublication/business/exception/BusinessException.html" title="class in com.visionnaire.webpublication.business.exception">BusinessException</a></code></dd></dl>
</li>
</ul>
<a name="getXmlSendEmail(int, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getXmlSendEmail</h4>
<pre>public&nbsp;String&nbsp;getXmlSendEmail(int&nbsp;numCommentaries,
                     String&nbsp;conteudo,
                     String&nbsp;site,
                     String&nbsp;data,
                     String&nbsp;urlConteudo,
                     String&nbsp;fileMore)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>numCommentaries</code> - </dd><dd><code>conteudo</code> - </dd><dd><code>site</code> - </dd><dd><code>data</code> - </dd><dd><code>urlConteudo</code> - </dd><dd><code>fileMore</code> - </dd>
<dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CommentarySenderImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ContactImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/CommentarySenderImpl.html" target="_top">Frames</a></li>
<li><a href="CommentarySenderImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
