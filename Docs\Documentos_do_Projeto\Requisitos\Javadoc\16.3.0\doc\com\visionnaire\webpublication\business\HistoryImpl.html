<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:21 BRT 2013 -->
<title>HistoryImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="HistoryImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoryImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/HistoryFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/HistoryWikiImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/HistoryImpl.html" target="_top">Frames</a></li>
<li><a href="HistoryImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business</div>
<h2 title="Class HistoryImpl" class="title">Class HistoryImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>BusinessObjectImpl</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/visionnaire/webpublication/pss/BO.html" title="class in com.visionnaire.webpublication.pss">BO</a></li>
<li>
<ul class="inheritance">
<li>HistoryImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">HistoryImpl</span>
extends <a href="../../../../com/visionnaire/webpublication/pss/BO.html" title="class in com.visionnaire.webpublication.pss">BO</a></pre>
<dl><dt><span class="strong">Author:</span></dt>
  <dd>danilo</dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../com/visionnaire/webpublication/business/pss/PServantHistoryImpl.html" title="class in com.visionnaire.webpublication.business.pss">PServantHistoryImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#ps">ps</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#HistoryImpl(java.util.Date, java.lang.String, com.visionnaire.webpublication.access.UserImpl, com.visionnaire.webpublication.business.visualcontent.VisualContentImpl, com.visionnaire.webpublication.business.ContentStateImpl, java.lang.String, java.lang.String)">HistoryImpl</a></strong>(Date&nbsp;serverDate,
           String&nbsp;justification,
           UserImpl&nbsp;user,
           <a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;visualContent,
           <a href="../../../../com/visionnaire/webpublication/business/ContentStateImpl.html" title="class in com.visionnaire.webpublication.business">ContentStateImpl</a>&nbsp;contentState,
           String&nbsp;contentWorkFlowState,
           String&nbsp;ipAddress)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#HistoryImpl(com.visionnaire.PSS.pid)">HistoryImpl</a></strong>(pid&nbsp;oid)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#getBodyField()">getBodyField</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/visionnaire/webpublication/business/ContentStateImpl.html" title="class in com.visionnaire.webpublication.business">ContentStateImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#getContentState()">getContentState</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#getContentWorkFlowState()">getContentWorkFlowState</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#getIpAddress()">getIpAddress</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#getJustification()">getJustification</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#getServerDate()">getServerDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>UserImpl</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#getUser()">getUser</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#getUserDate()">getUserDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#getVisualContent()">getVisualContent</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#pssDeleteFields()">pssDeleteFields</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#setBodyField(java.lang.String)">setBodyField</a></strong>(String&nbsp;bodyField)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#setContentState(com.visionnaire.webpublication.business.ContentStateImpl)">setContentState</a></strong>(<a href="../../../../com/visionnaire/webpublication/business/ContentStateImpl.html" title="class in com.visionnaire.webpublication.business">ContentStateImpl</a>&nbsp;contentState)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#setContentWorkFlowState(java.lang.String)">setContentWorkFlowState</a></strong>(String&nbsp;contentWorkFlowState)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#setIpAddress(java.lang.String)">setIpAddress</a></strong>(String&nbsp;ipAddress)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#setJustification(java.lang.String)">setJustification</a></strong>(String&nbsp;justification)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#setServerDate(java.util.Date)">setServerDate</a></strong>(Date&nbsp;serverDate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#setUser(com.visionnaire.webpublication.access.UserImpl)">setUser</a></strong>(UserImpl&nbsp;user)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html#setVisualContent(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">setVisualContent</a></strong>(<a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;visualContent)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_BO">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;<a href="../../../../com/visionnaire/webpublication/pss/BO.html" title="class in com.visionnaire.webpublication.pss">BO</a></h3>
<code><a href="../../../../com/visionnaire/webpublication/pss/BO.html#toSQL(java.lang.String)">toSQL</a>, <a href="../../../../com/visionnaire/webpublication/pss/BO.html#toSQL(java.lang.String, int)">toSQL</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_BusinessObjectImpl">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;BusinessObjectImpl</h3>
<code>getPid, pssServant, setPS</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, getClassId, hashCode, isDying, pssDelete, pssDestroy, pssLock, toInternal, toInternal, toString, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ps">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ps</h4>
<pre>private final&nbsp;<a href="../../../../com/visionnaire/webpublication/business/pss/PServantHistoryImpl.html" title="class in com.visionnaire.webpublication.business.pss">PServantHistoryImpl</a> ps</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HistoryImpl(com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HistoryImpl</h4>
<pre>public&nbsp;HistoryImpl(pid&nbsp;oid)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>oid</code> - </dd></dl>
</li>
</ul>
<a name="HistoryImpl(java.util.Date, java.lang.String, com.visionnaire.webpublication.access.UserImpl, com.visionnaire.webpublication.business.visualcontent.VisualContentImpl, com.visionnaire.webpublication.business.ContentStateImpl, java.lang.String, java.lang.String)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HistoryImpl</h4>
<pre>public&nbsp;HistoryImpl(Date&nbsp;serverDate,
           String&nbsp;justification,
           UserImpl&nbsp;user,
           <a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;visualContent,
           <a href="../../../../com/visionnaire/webpublication/business/ContentStateImpl.html" title="class in com.visionnaire.webpublication.business">ContentStateImpl</a>&nbsp;contentState,
           String&nbsp;contentWorkFlowState,
           String&nbsp;ipAddress)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>serverDate</code> - </dd><dd><code>justification</code> - </dd><dd><code>user</code> - </dd><dd><code>visualContent</code> - </dd><dd><code>contentState</code> - </dd><dd><code>contentWorkFlowState</code> - </dd><dd><code>ipAddress</code> - </dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssDeleteFields()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssDeleteFields</h4>
<pre>public&nbsp;void&nbsp;pssDeleteFields()
                     throws NotDisconnectedException</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in class&nbsp;<code>PersistentImplBase</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>NotDisconnectedException</code></dd><dt><span class="strong">See Also:</span></dt><dd><code>PersistentImplBase.pssDeleteFields()</code></dd></dl>
</li>
</ul>
<a name="getServerDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getServerDate</h4>
<pre>public&nbsp;Date&nbsp;getServerDate()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>Date</dd></dl>
</li>
</ul>
<a name="setServerDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setServerDate</h4>
<pre>public&nbsp;void&nbsp;setServerDate(Date&nbsp;serverDate)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>serverDate</code> - </dd></dl>
</li>
</ul>
<a name="getJustification()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJustification</h4>
<pre>public&nbsp;String&nbsp;getJustification()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setJustification(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJustification</h4>
<pre>public&nbsp;void&nbsp;setJustification(String&nbsp;justification)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>justification</code> - </dd></dl>
</li>
</ul>
<a name="getBodyField()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBodyField</h4>
<pre>public&nbsp;String&nbsp;getBodyField()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setBodyField(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBodyField</h4>
<pre>public&nbsp;void&nbsp;setBodyField(String&nbsp;bodyField)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>bodyField</code> - </dd></dl>
</li>
</ul>
<a name="getUser()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUser</h4>
<pre>public&nbsp;UserImpl&nbsp;getUser()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>UserImpl</dd></dl>
</li>
</ul>
<a name="setUser(com.visionnaire.webpublication.access.UserImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUser</h4>
<pre>public&nbsp;void&nbsp;setUser(UserImpl&nbsp;user)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>user</code> - </dd></dl>
</li>
</ul>
<a name="getVisualContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVisualContent</h4>
<pre>public&nbsp;<a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;getVisualContent()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>VisualContentImpl</dd></dl>
</li>
</ul>
<a name="setVisualContent(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisualContent</h4>
<pre>public&nbsp;void&nbsp;setVisualContent(<a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;visualContent)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>visualContent</code> - </dd></dl>
</li>
</ul>
<a name="getContentState()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentState</h4>
<pre>public&nbsp;<a href="../../../../com/visionnaire/webpublication/business/ContentStateImpl.html" title="class in com.visionnaire.webpublication.business">ContentStateImpl</a>&nbsp;getContentState()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>ContentStateImpl</dd></dl>
</li>
</ul>
<a name="setContentState(com.visionnaire.webpublication.business.ContentStateImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContentState</h4>
<pre>public&nbsp;void&nbsp;setContentState(<a href="../../../../com/visionnaire/webpublication/business/ContentStateImpl.html" title="class in com.visionnaire.webpublication.business">ContentStateImpl</a>&nbsp;contentState)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>contentState</code> - </dd></dl>
</li>
</ul>
<a name="getContentWorkFlowState()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentWorkFlowState</h4>
<pre>public&nbsp;String&nbsp;getContentWorkFlowState()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>ContentWorkFlowStateImpl</dd></dl>
</li>
</ul>
<a name="setContentWorkFlowState(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContentWorkFlowState</h4>
<pre>public&nbsp;void&nbsp;setContentWorkFlowState(String&nbsp;contentWorkFlowState)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>contentWorkFlowState</code> - </dd></dl>
</li>
</ul>
<a name="getUserDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUserDate</h4>
<pre>public&nbsp;String&nbsp;getUserDate()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setIpAddress(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIpAddress</h4>
<pre>public&nbsp;void&nbsp;setIpAddress(String&nbsp;ipAddress)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>ipAddress</code> - </dd></dl>
</li>
</ul>
<a name="getIpAddress()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getIpAddress</h4>
<pre>public&nbsp;String&nbsp;getIpAddress()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HistoryImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/HistoryFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/HistoryWikiImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/HistoryImpl.html" target="_top">Frames</a></li>
<li><a href="HistoryImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
