<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:23 BRT 2013 -->
<title>RespostaPDFTransient</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="RespostaPDFTransient";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RespostaPDFTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSFeedWarmer.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" target="_top">Frames</a></li>
<li><a href="RespostaPDFTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent</div>
<h2 title="Class RespostaPDFTransient" class="title">Class RespostaPDFTransient</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>RespostaPDFTransient</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="strong">RespostaPDFTransient</span>
extends Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#numero">numero</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#pergunta">pergunta</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#resposta">resposta</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#RespostaPDFTransient()">RespostaPDFTransient</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#RespostaPDFTransient(java.lang.String, java.lang.String, java.lang.String)">RespostaPDFTransient</a></strong>(String&nbsp;numero,
                    String&nbsp;pergunta,
                    String&nbsp;resposta)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#getNumero()">getNumero</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#getPergunta()">getPergunta</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#getResposta()">getResposta</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#setNumero(java.lang.String)">setNumero</a></strong>(String&nbsp;numero)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#setPergunta(java.lang.String)">setPergunta</a></strong>(String&nbsp;pergunta)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html#setResposta(java.lang.String)">setResposta</a></strong>(String&nbsp;resposta)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="numero">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>numero</h4>
<pre>private&nbsp;String numero</pre>
</li>
</ul>
<a name="pergunta">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pergunta</h4>
<pre>private&nbsp;String pergunta</pre>
</li>
</ul>
<a name="resposta">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>resposta</h4>
<pre>private&nbsp;String resposta</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RespostaPDFTransient(java.lang.String, java.lang.String, java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RespostaPDFTransient</h4>
<pre>public&nbsp;RespostaPDFTransient(String&nbsp;numero,
                    String&nbsp;pergunta,
                    String&nbsp;resposta)</pre>
</li>
</ul>
<a name="RespostaPDFTransient()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RespostaPDFTransient</h4>
<pre>public&nbsp;RespostaPDFTransient()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getNumero()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumero</h4>
<pre>public&nbsp;String&nbsp;getNumero()</pre>
</li>
</ul>
<a name="setNumero(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumero</h4>
<pre>public&nbsp;void&nbsp;setNumero(String&nbsp;numero)</pre>
</li>
</ul>
<a name="getPergunta()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPergunta</h4>
<pre>public&nbsp;String&nbsp;getPergunta()</pre>
</li>
</ul>
<a name="setPergunta(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPergunta</h4>
<pre>public&nbsp;void&nbsp;setPergunta(String&nbsp;pergunta)</pre>
</li>
</ul>
<a name="getResposta()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResposta</h4>
<pre>public&nbsp;String&nbsp;getResposta()</pre>
</li>
</ul>
<a name="setResposta(java.lang.String)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setResposta</h4>
<pre>public&nbsp;void&nbsp;setResposta(String&nbsp;resposta)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RespostaPDFTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSFeedWarmer.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" target="_top">Frames</a></li>
<li><a href="RespostaPDFTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
