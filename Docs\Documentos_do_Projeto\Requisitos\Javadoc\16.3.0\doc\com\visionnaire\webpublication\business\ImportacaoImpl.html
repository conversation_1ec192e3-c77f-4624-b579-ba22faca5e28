<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:21 BRT 2013 -->
<title>ImportacaoImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="ImportacaoImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ImportacaoImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/ImageFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/InheritComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/ImportacaoImpl.html" target="_top">Frames</a></li>
<li><a href="ImportacaoImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business</div>
<h2 title="Class ImportacaoImpl" class="title">Class ImportacaoImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>ImportacaoImpl</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="strong">ImportacaoImpl</span>
extends Object</pre>
<dl><dt><span class="strong">Author:</span></dt>
  <dd>andres</dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#allowCommentaries">allowCommentaries</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#anexosIds">anexosIds</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#approved">approved</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#body">body</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#componentPid">componentPid</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#contentAuthorEmail">contentAuthorEmail</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#contentAuthorName">contentAuthorName</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#contentDescription">contentDescription</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#credentials">credentials</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#editionDate">editionDate</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#emailResponsavel">emailResponsavel</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#fatherContent">fatherContent</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#filesId">filesId</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private Long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#frontFileId">frontFileId</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private Long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#frontImageId">frontImageId</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#gerarArquivoDetalhe">gerarArquivoDetalhe</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private Long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#groupNamePid">groupNamePid</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#hat">hat</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#imagesAnexas">imagesAnexas</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#imagesId">imagesId</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#internalTitle">internalTitle</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#ipAddress">ipAddress</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isNewWindow">isNewWindow</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isToShow">isToShow</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#officialContentPid">officialContentPid</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#preModeracao">preModeracao</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#preview">preview</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#priority">priority</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private Date</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#removeDate">removeDate</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#selectedTags">selectedTags</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#serEncontradoBusca">serEncontradoBusca</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#sessionId">sessionId</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#tiedContents">tiedContents</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#title">title</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private List&lt;String&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#typedTags">typedTags</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#url">url</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#urlContentPid">urlContentPid</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#urlExternal">urlExternal</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#wiki">wiki</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private Long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#workFlowPid">workFlowPid</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#ImportacaoImpl()">ImportacaoImpl</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#ImportacaoImpl(long, java.util.Date, java.util.Date, int, java.lang.Long, java.lang.Long, boolean, java.util.List, java.lang.String, java.lang.Long, java.lang.Long, java.util.List, java.util.List, java.util.List, java.lang.String, java.lang.String, java.util.List, java.util.List, com.visionnaire.webpublication.business.visualcontent.VisualContentImpl, java.lang.String, int, boolean, boolean, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, boolean, boolean, long, boolean, boolean, boolean, boolean, java.util.List)">ImportacaoImpl</a></strong>(long&nbsp;componentPid,
              Date&nbsp;editionDate,
              Date&nbsp;removeDate,
              int&nbsp;priority,
              Long&nbsp;groupNamePid,
              Long&nbsp;workFlowPid,
              boolean&nbsp;approved,
              List&lt;Long&gt;&nbsp;tiedContents,
              String&nbsp;ipAddress,
              Long&nbsp;frontImageId,
              Long&nbsp;frontFileId,
              List&lt;Long&gt;&nbsp;anexosIds,
              List&lt;Long&gt;&nbsp;imagesId,
              List&lt;Long&gt;&nbsp;filesId,
              String&nbsp;credentials,
              String&nbsp;sessionId,
              List&lt;Long&gt;&nbsp;selectedTags,
              List&lt;String&gt;&nbsp;typedTags,
              <a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;fatherContent,
              String&nbsp;url,
              int&nbsp;urlExternal,
              boolean&nbsp;isNewWindow,
              boolean&nbsp;isToShow,
              String&nbsp;emailResponsavel,
              String&nbsp;body,
              String&nbsp;internalTitle,
              String&nbsp;contentDescription,
              String&nbsp;hat,
              String&nbsp;contentAuthorName,
              String&nbsp;contentAuthorEmail,
              String&nbsp;title,
              String&nbsp;urlContentPid,
              boolean&nbsp;preview,
              boolean&nbsp;wiki,
              long&nbsp;officialContentPid,
              boolean&nbsp;allowCommentaries,
              boolean&nbsp;preModeracao,
              boolean&nbsp;gerarArquivoDetalhe,
              boolean&nbsp;serEncontradoBusca,
              List&lt;Long&gt;&nbsp;imagesAnexas)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getAnexosIds()">getAnexosIds</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getBody()">getBody</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getComponentPid()">getComponentPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getContentAuthorEmail()">getContentAuthorEmail</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getContentAuthorName()">getContentAuthorName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getContentDescription()">getContentDescription</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getCredentials()">getCredentials</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getEditionDate()">getEditionDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getEmailResponsavel()">getEmailResponsavel</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getFatherContent()">getFatherContent</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getFilesId()">getFilesId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getFrontFileId()">getFrontFileId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getFrontImageId()">getFrontImageId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getGroupNamePid()">getGroupNamePid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getHat()">getHat</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getImagesAnexas()">getImagesAnexas</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getImagesId()">getImagesId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getInternalTitle()">getInternalTitle</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getIpAddress()">getIpAddress</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getOfficialContentPid()">getOfficialContentPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getPriority()">getPriority</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getRemoveDate()">getRemoveDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getSelectedTags()">getSelectedTags</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getSessionId()">getSessionId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>List&lt;Long&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getTiedContents()">getTiedContents</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getTitle()">getTitle</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>List&lt;String&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getTypedTags()">getTypedTags</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getUrl()">getUrl</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getUrlContentPid()">getUrlContentPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getUrlExternal()">getUrlExternal</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Long</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#getWorkFlowPid()">getWorkFlowPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isAllowCommentaries()">isAllowCommentaries</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isApproved()">isApproved</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isGerarArquivoDetalhe()">isGerarArquivoDetalhe</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isNewWindow()">isNewWindow</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isPreModeracao()">isPreModeracao</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isPreview()">isPreview</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isSerEncontradoBusca()">isSerEncontradoBusca</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isToShow()">isToShow</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#isWiki()">isWiki</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setAllowCommentaries(boolean)">setAllowCommentaries</a></strong>(boolean&nbsp;allowCommentaries)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setAnexosIds(java.util.List)">setAnexosIds</a></strong>(List&lt;Long&gt;&nbsp;anexosIds)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setApproved(boolean)">setApproved</a></strong>(boolean&nbsp;approved)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setBody(java.lang.String)">setBody</a></strong>(String&nbsp;body)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setComponentPid(long)">setComponentPid</a></strong>(long&nbsp;componentPid)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setContentAuthorEmail(java.lang.String)">setContentAuthorEmail</a></strong>(String&nbsp;contentAuthorEmail)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setContentAuthorName(java.lang.String)">setContentAuthorName</a></strong>(String&nbsp;contentAuthorName)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setContentDescription(java.lang.String)">setContentDescription</a></strong>(String&nbsp;contentDescription)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setCredentials(java.lang.String)">setCredentials</a></strong>(String&nbsp;credentials)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setEditionDate(java.util.Date)">setEditionDate</a></strong>(Date&nbsp;editionDate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setEmailResponsavel(java.lang.String)">setEmailResponsavel</a></strong>(String&nbsp;emailResponsavel)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setFatherContent(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">setFatherContent</a></strong>(<a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;fatherContent)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setFilesId(java.util.List)">setFilesId</a></strong>(List&lt;Long&gt;&nbsp;filesId)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setFrontFileId(java.lang.Long)">setFrontFileId</a></strong>(Long&nbsp;frontFileId)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setFrontImageId(java.lang.Long)">setFrontImageId</a></strong>(Long&nbsp;frontImageId)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setGerarArquivoDetalhe(boolean)">setGerarArquivoDetalhe</a></strong>(boolean&nbsp;gerarArquivoDetalhe)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setGroupNamePid(java.lang.Long)">setGroupNamePid</a></strong>(Long&nbsp;groupNamePid)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setHat(java.lang.String)">setHat</a></strong>(String&nbsp;hat)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setImagesAnexas(java.util.List)">setImagesAnexas</a></strong>(List&lt;Long&gt;&nbsp;imagesAnexas)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setImagesId(java.util.List)">setImagesId</a></strong>(List&lt;Long&gt;&nbsp;imagesId)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setInternalTitle(java.lang.String)">setInternalTitle</a></strong>(String&nbsp;internalTitle)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setIpAddress(java.lang.String)">setIpAddress</a></strong>(String&nbsp;ipAddress)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setNewWindow(boolean)">setNewWindow</a></strong>(boolean&nbsp;isNewWindow)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setOfficialContentPid(long)">setOfficialContentPid</a></strong>(long&nbsp;officialContentPid)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setPreModeracao(boolean)">setPreModeracao</a></strong>(boolean&nbsp;preModeracao)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setPreview(boolean)">setPreview</a></strong>(boolean&nbsp;preview)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setPriority(int)">setPriority</a></strong>(int&nbsp;priority)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setRemoveDate(java.util.Date)">setRemoveDate</a></strong>(Date&nbsp;removeDate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setSelectedTags(java.util.List)">setSelectedTags</a></strong>(List&lt;Long&gt;&nbsp;selectedTags)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setSerEncontradoBusca(boolean)">setSerEncontradoBusca</a></strong>(boolean&nbsp;serEncontradoBusca)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setSessionId(java.lang.String)">setSessionId</a></strong>(String&nbsp;sessionId)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setTiedContents(java.util.List)">setTiedContents</a></strong>(List&lt;Long&gt;&nbsp;tiedContents)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setTitle(java.lang.String)">setTitle</a></strong>(String&nbsp;title)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setToShow(boolean)">setToShow</a></strong>(boolean&nbsp;isToShow)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setTypedTags(java.util.List)">setTypedTags</a></strong>(List&lt;String&gt;&nbsp;typedTags)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setUrl(java.lang.String)">setUrl</a></strong>(String&nbsp;url)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setUrlContentPid(java.lang.String)">setUrlContentPid</a></strong>(String&nbsp;urlContentPid)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setUrlExternal(int)">setUrlExternal</a></strong>(int&nbsp;urlExternal)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setWiki(boolean)">setWiki</a></strong>(boolean&nbsp;wiki)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html#setWorkFlowPid(java.lang.Long)">setWorkFlowPid</a></strong>(Long&nbsp;workFlowPid)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="componentPid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>componentPid</h4>
<pre>private&nbsp;long componentPid</pre>
</li>
</ul>
<a name="editionDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editionDate</h4>
<pre>private&nbsp;Date editionDate</pre>
</li>
</ul>
<a name="removeDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeDate</h4>
<pre>private&nbsp;Date removeDate</pre>
</li>
</ul>
<a name="priority">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>priority</h4>
<pre>private&nbsp;int priority</pre>
</li>
</ul>
<a name="groupNamePid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupNamePid</h4>
<pre>private&nbsp;Long groupNamePid</pre>
</li>
</ul>
<a name="workFlowPid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workFlowPid</h4>
<pre>private&nbsp;Long workFlowPid</pre>
</li>
</ul>
<a name="approved">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>approved</h4>
<pre>private&nbsp;boolean approved</pre>
</li>
</ul>
<a name="tiedContents">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tiedContents</h4>
<pre>private&nbsp;List&lt;Long&gt; tiedContents</pre>
</li>
</ul>
<a name="ipAddress">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ipAddress</h4>
<pre>private&nbsp;String ipAddress</pre>
</li>
</ul>
<a name="frontImageId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontImageId</h4>
<pre>private&nbsp;Long frontImageId</pre>
</li>
</ul>
<a name="frontFileId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>frontFileId</h4>
<pre>private&nbsp;Long frontFileId</pre>
</li>
</ul>
<a name="anexosIds">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>anexosIds</h4>
<pre>private&nbsp;List&lt;Long&gt; anexosIds</pre>
</li>
</ul>
<a name="imagesId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imagesId</h4>
<pre>private&nbsp;List&lt;Long&gt; imagesId</pre>
</li>
</ul>
<a name="filesId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>filesId</h4>
<pre>private&nbsp;List&lt;Long&gt; filesId</pre>
</li>
</ul>
<a name="credentials">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>credentials</h4>
<pre>private&nbsp;String credentials</pre>
</li>
</ul>
<a name="sessionId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sessionId</h4>
<pre>private&nbsp;String sessionId</pre>
</li>
</ul>
<a name="selectedTags">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>selectedTags</h4>
<pre>private&nbsp;List&lt;Long&gt; selectedTags</pre>
</li>
</ul>
<a name="typedTags">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>typedTags</h4>
<pre>private&nbsp;List&lt;String&gt; typedTags</pre>
</li>
</ul>
<a name="fatherContent">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherContent</h4>
<pre>private&nbsp;<a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a> fatherContent</pre>
</li>
</ul>
<a name="url">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>url</h4>
<pre>private&nbsp;String url</pre>
</li>
</ul>
<a name="urlExternal">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>urlExternal</h4>
<pre>private&nbsp;int urlExternal</pre>
</li>
</ul>
<a name="isNewWindow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNewWindow</h4>
<pre>private&nbsp;boolean isNewWindow</pre>
</li>
</ul>
<a name="isToShow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isToShow</h4>
<pre>private&nbsp;boolean isToShow</pre>
</li>
</ul>
<a name="emailResponsavel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emailResponsavel</h4>
<pre>private&nbsp;String emailResponsavel</pre>
</li>
</ul>
<a name="body">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>body</h4>
<pre>private&nbsp;String body</pre>
</li>
</ul>
<a name="internalTitle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>internalTitle</h4>
<pre>private&nbsp;String internalTitle</pre>
</li>
</ul>
<a name="contentDescription">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentDescription</h4>
<pre>private&nbsp;String contentDescription</pre>
</li>
</ul>
<a name="hat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hat</h4>
<pre>private&nbsp;String hat</pre>
</li>
</ul>
<a name="contentAuthorName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentAuthorName</h4>
<pre>private&nbsp;String contentAuthorName</pre>
</li>
</ul>
<a name="contentAuthorEmail">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentAuthorEmail</h4>
<pre>private&nbsp;String contentAuthorEmail</pre>
</li>
</ul>
<a name="title">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>title</h4>
<pre>private&nbsp;String title</pre>
</li>
</ul>
<a name="urlContentPid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>urlContentPid</h4>
<pre>private&nbsp;String urlContentPid</pre>
</li>
</ul>
<a name="preview">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>preview</h4>
<pre>private&nbsp;boolean preview</pre>
</li>
</ul>
<a name="wiki">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wiki</h4>
<pre>private&nbsp;boolean wiki</pre>
</li>
</ul>
<a name="officialContentPid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>officialContentPid</h4>
<pre>private&nbsp;long officialContentPid</pre>
</li>
</ul>
<a name="allowCommentaries">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allowCommentaries</h4>
<pre>private&nbsp;boolean allowCommentaries</pre>
</li>
</ul>
<a name="preModeracao">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>preModeracao</h4>
<pre>private&nbsp;boolean preModeracao</pre>
</li>
</ul>
<a name="gerarArquivoDetalhe">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerarArquivoDetalhe</h4>
<pre>private&nbsp;boolean gerarArquivoDetalhe</pre>
</li>
</ul>
<a name="serEncontradoBusca">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serEncontradoBusca</h4>
<pre>private&nbsp;boolean serEncontradoBusca</pre>
</li>
</ul>
<a name="imagesAnexas">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>imagesAnexas</h4>
<pre>private&nbsp;List&lt;Long&gt; imagesAnexas</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ImportacaoImpl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ImportacaoImpl</h4>
<pre>public&nbsp;ImportacaoImpl()</pre>
</li>
</ul>
<a name="ImportacaoImpl(long, java.util.Date, java.util.Date, int, java.lang.Long, java.lang.Long, boolean, java.util.List, java.lang.String, java.lang.Long, java.lang.Long, java.util.List, java.util.List, java.util.List, java.lang.String, java.lang.String, java.util.List, java.util.List, com.visionnaire.webpublication.business.visualcontent.VisualContentImpl, java.lang.String, int, boolean, boolean, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, boolean, boolean, long, boolean, boolean, boolean, boolean, java.util.List)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ImportacaoImpl</h4>
<pre>public&nbsp;ImportacaoImpl(long&nbsp;componentPid,
              Date&nbsp;editionDate,
              Date&nbsp;removeDate,
              int&nbsp;priority,
              Long&nbsp;groupNamePid,
              Long&nbsp;workFlowPid,
              boolean&nbsp;approved,
              List&lt;Long&gt;&nbsp;tiedContents,
              String&nbsp;ipAddress,
              Long&nbsp;frontImageId,
              Long&nbsp;frontFileId,
              List&lt;Long&gt;&nbsp;anexosIds,
              List&lt;Long&gt;&nbsp;imagesId,
              List&lt;Long&gt;&nbsp;filesId,
              String&nbsp;credentials,
              String&nbsp;sessionId,
              List&lt;Long&gt;&nbsp;selectedTags,
              List&lt;String&gt;&nbsp;typedTags,
              <a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;fatherContent,
              String&nbsp;url,
              int&nbsp;urlExternal,
              boolean&nbsp;isNewWindow,
              boolean&nbsp;isToShow,
              String&nbsp;emailResponsavel,
              String&nbsp;body,
              String&nbsp;internalTitle,
              String&nbsp;contentDescription,
              String&nbsp;hat,
              String&nbsp;contentAuthorName,
              String&nbsp;contentAuthorEmail,
              String&nbsp;title,
              String&nbsp;urlContentPid,
              boolean&nbsp;preview,
              boolean&nbsp;wiki,
              long&nbsp;officialContentPid,
              boolean&nbsp;allowCommentaries,
              boolean&nbsp;preModeracao,
              boolean&nbsp;gerarArquivoDetalhe,
              boolean&nbsp;serEncontradoBusca,
              List&lt;Long&gt;&nbsp;imagesAnexas)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>componentPid</code> - </dd><dd><code>editionDate</code> - </dd><dd><code>removeDate</code> - </dd><dd><code>priority</code> - </dd><dd><code>groupNamePid</code> - </dd><dd><code>workFlowPid</code> - </dd><dd><code>approved</code> - </dd><dd><code>tiedContents</code> - </dd><dd><code>ipAddress</code> - </dd><dd><code>frontImageId</code> - </dd><dd><code>frontFileId</code> - </dd><dd><code>anexosIds</code> - </dd><dd><code>imagesId</code> - </dd><dd><code>filesId</code> - </dd><dd><code>credentials</code> - </dd><dd><code>sessionId</code> - </dd><dd><code>selectedTags</code> - </dd><dd><code>typedTags</code> - </dd><dd><code>fatherContent</code> - </dd><dd><code>url</code> - </dd><dd><code>urlExternal</code> - </dd><dd><code>isNewWindow</code> - </dd><dd><code>isToShow</code> - </dd><dd><code>emailResponsavel</code> - </dd><dd><code>body</code> - </dd><dd><code>internalTitle</code> - </dd><dd><code>contentDescription</code> - </dd><dd><code>hat</code> - </dd><dd><code>contentAuthorName</code> - </dd><dd><code>contentAuthorEmail</code> - </dd><dd><code>title</code> - </dd><dd><code>urlContentPid</code> - </dd><dd><code>preview</code> - </dd><dd><code>wiki</code> - </dd><dd><code>officialContentPid</code> - </dd><dd><code>allowCommentaries</code> - </dd><dd><code>preModeracao</code> - </dd><dd><code>gerarArquivoDetalhe</code> - </dd><dd><code>serEncontradoBusca</code> - </dd><dd><code>imagesAnexas</code> - </dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getComponentPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComponentPid</h4>
<pre>public&nbsp;long&nbsp;getComponentPid()</pre>
</li>
</ul>
<a name="setComponentPid(long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setComponentPid</h4>
<pre>public&nbsp;void&nbsp;setComponentPid(long&nbsp;componentPid)</pre>
</li>
</ul>
<a name="getEditionDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEditionDate</h4>
<pre>public&nbsp;Date&nbsp;getEditionDate()</pre>
</li>
</ul>
<a name="setEditionDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditionDate</h4>
<pre>public&nbsp;void&nbsp;setEditionDate(Date&nbsp;editionDate)</pre>
</li>
</ul>
<a name="getRemoveDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemoveDate</h4>
<pre>public&nbsp;Date&nbsp;getRemoveDate()</pre>
</li>
</ul>
<a name="setRemoveDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemoveDate</h4>
<pre>public&nbsp;void&nbsp;setRemoveDate(Date&nbsp;removeDate)</pre>
</li>
</ul>
<a name="getPriority()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriority</h4>
<pre>public&nbsp;int&nbsp;getPriority()</pre>
</li>
</ul>
<a name="setPriority(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriority</h4>
<pre>public&nbsp;void&nbsp;setPriority(int&nbsp;priority)</pre>
</li>
</ul>
<a name="getGroupNamePid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroupNamePid</h4>
<pre>public&nbsp;Long&nbsp;getGroupNamePid()</pre>
</li>
</ul>
<a name="setGroupNamePid(java.lang.Long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroupNamePid</h4>
<pre>public&nbsp;void&nbsp;setGroupNamePid(Long&nbsp;groupNamePid)</pre>
</li>
</ul>
<a name="getWorkFlowPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkFlowPid</h4>
<pre>public&nbsp;Long&nbsp;getWorkFlowPid()</pre>
</li>
</ul>
<a name="setWorkFlowPid(java.lang.Long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkFlowPid</h4>
<pre>public&nbsp;void&nbsp;setWorkFlowPid(Long&nbsp;workFlowPid)</pre>
</li>
</ul>
<a name="isApproved()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isApproved</h4>
<pre>public&nbsp;boolean&nbsp;isApproved()</pre>
</li>
</ul>
<a name="setApproved(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApproved</h4>
<pre>public&nbsp;void&nbsp;setApproved(boolean&nbsp;approved)</pre>
</li>
</ul>
<a name="getTiedContents()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTiedContents</h4>
<pre>public&nbsp;List&lt;Long&gt;&nbsp;getTiedContents()</pre>
</li>
</ul>
<a name="setTiedContents(java.util.List)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTiedContents</h4>
<pre>public&nbsp;void&nbsp;setTiedContents(List&lt;Long&gt;&nbsp;tiedContents)</pre>
</li>
</ul>
<a name="getIpAddress()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIpAddress</h4>
<pre>public&nbsp;String&nbsp;getIpAddress()</pre>
</li>
</ul>
<a name="setIpAddress(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIpAddress</h4>
<pre>public&nbsp;void&nbsp;setIpAddress(String&nbsp;ipAddress)</pre>
</li>
</ul>
<a name="getFrontImageId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrontImageId</h4>
<pre>public&nbsp;Long&nbsp;getFrontImageId()</pre>
</li>
</ul>
<a name="setFrontImageId(java.lang.Long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrontImageId</h4>
<pre>public&nbsp;void&nbsp;setFrontImageId(Long&nbsp;frontImageId)</pre>
</li>
</ul>
<a name="getFrontFileId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrontFileId</h4>
<pre>public&nbsp;Long&nbsp;getFrontFileId()</pre>
</li>
</ul>
<a name="setFrontFileId(java.lang.Long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrontFileId</h4>
<pre>public&nbsp;void&nbsp;setFrontFileId(Long&nbsp;frontFileId)</pre>
</li>
</ul>
<a name="getAnexosIds()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnexosIds</h4>
<pre>public&nbsp;List&lt;Long&gt;&nbsp;getAnexosIds()</pre>
</li>
</ul>
<a name="setAnexosIds(java.util.List)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAnexosIds</h4>
<pre>public&nbsp;void&nbsp;setAnexosIds(List&lt;Long&gt;&nbsp;anexosIds)</pre>
</li>
</ul>
<a name="getImagesId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImagesId</h4>
<pre>public&nbsp;List&lt;Long&gt;&nbsp;getImagesId()</pre>
</li>
</ul>
<a name="setImagesId(java.util.List)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setImagesId</h4>
<pre>public&nbsp;void&nbsp;setImagesId(List&lt;Long&gt;&nbsp;imagesId)</pre>
</li>
</ul>
<a name="getFilesId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilesId</h4>
<pre>public&nbsp;List&lt;Long&gt;&nbsp;getFilesId()</pre>
</li>
</ul>
<a name="setFilesId(java.util.List)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFilesId</h4>
<pre>public&nbsp;void&nbsp;setFilesId(List&lt;Long&gt;&nbsp;filesId)</pre>
</li>
</ul>
<a name="getCredentials()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCredentials</h4>
<pre>public&nbsp;String&nbsp;getCredentials()</pre>
</li>
</ul>
<a name="setCredentials(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCredentials</h4>
<pre>public&nbsp;void&nbsp;setCredentials(String&nbsp;credentials)</pre>
</li>
</ul>
<a name="getSessionId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSessionId</h4>
<pre>public&nbsp;String&nbsp;getSessionId()</pre>
</li>
</ul>
<a name="setSessionId(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSessionId</h4>
<pre>public&nbsp;void&nbsp;setSessionId(String&nbsp;sessionId)</pre>
</li>
</ul>
<a name="getSelectedTags()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectedTags</h4>
<pre>public&nbsp;List&lt;Long&gt;&nbsp;getSelectedTags()</pre>
</li>
</ul>
<a name="setSelectedTags(java.util.List)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectedTags</h4>
<pre>public&nbsp;void&nbsp;setSelectedTags(List&lt;Long&gt;&nbsp;selectedTags)</pre>
</li>
</ul>
<a name="getTypedTags()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTypedTags</h4>
<pre>public&nbsp;List&lt;String&gt;&nbsp;getTypedTags()</pre>
</li>
</ul>
<a name="setTypedTags(java.util.List)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTypedTags</h4>
<pre>public&nbsp;void&nbsp;setTypedTags(List&lt;String&gt;&nbsp;typedTags)</pre>
</li>
</ul>
<a name="getFatherContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFatherContent</h4>
<pre>public&nbsp;<a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;getFatherContent()</pre>
</li>
</ul>
<a name="setFatherContent(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFatherContent</h4>
<pre>public&nbsp;void&nbsp;setFatherContent(<a href="../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;fatherContent)</pre>
</li>
</ul>
<a name="getUrl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUrl</h4>
<pre>public&nbsp;String&nbsp;getUrl()</pre>
</li>
</ul>
<a name="setUrl(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUrl</h4>
<pre>public&nbsp;void&nbsp;setUrl(String&nbsp;url)</pre>
</li>
</ul>
<a name="getUrlExternal()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUrlExternal</h4>
<pre>public&nbsp;int&nbsp;getUrlExternal()</pre>
</li>
</ul>
<a name="setUrlExternal(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUrlExternal</h4>
<pre>public&nbsp;void&nbsp;setUrlExternal(int&nbsp;urlExternal)</pre>
</li>
</ul>
<a name="isNewWindow()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNewWindow</h4>
<pre>public&nbsp;boolean&nbsp;isNewWindow()</pre>
</li>
</ul>
<a name="setNewWindow(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewWindow</h4>
<pre>public&nbsp;void&nbsp;setNewWindow(boolean&nbsp;isNewWindow)</pre>
</li>
</ul>
<a name="isToShow()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isToShow</h4>
<pre>public&nbsp;boolean&nbsp;isToShow()</pre>
</li>
</ul>
<a name="setToShow(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToShow</h4>
<pre>public&nbsp;void&nbsp;setToShow(boolean&nbsp;isToShow)</pre>
</li>
</ul>
<a name="getEmailResponsavel()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEmailResponsavel</h4>
<pre>public&nbsp;String&nbsp;getEmailResponsavel()</pre>
</li>
</ul>
<a name="setEmailResponsavel(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEmailResponsavel</h4>
<pre>public&nbsp;void&nbsp;setEmailResponsavel(String&nbsp;emailResponsavel)</pre>
</li>
</ul>
<a name="getBody()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBody</h4>
<pre>public&nbsp;String&nbsp;getBody()</pre>
</li>
</ul>
<a name="setBody(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBody</h4>
<pre>public&nbsp;void&nbsp;setBody(String&nbsp;body)</pre>
</li>
</ul>
<a name="getInternalTitle()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInternalTitle</h4>
<pre>public&nbsp;String&nbsp;getInternalTitle()</pre>
</li>
</ul>
<a name="setInternalTitle(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInternalTitle</h4>
<pre>public&nbsp;void&nbsp;setInternalTitle(String&nbsp;internalTitle)</pre>
</li>
</ul>
<a name="getContentDescription()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentDescription</h4>
<pre>public&nbsp;String&nbsp;getContentDescription()</pre>
</li>
</ul>
<a name="setContentDescription(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContentDescription</h4>
<pre>public&nbsp;void&nbsp;setContentDescription(String&nbsp;contentDescription)</pre>
</li>
</ul>
<a name="getHat()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHat</h4>
<pre>public&nbsp;String&nbsp;getHat()</pre>
</li>
</ul>
<a name="setHat(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHat</h4>
<pre>public&nbsp;void&nbsp;setHat(String&nbsp;hat)</pre>
</li>
</ul>
<a name="getContentAuthorName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentAuthorName</h4>
<pre>public&nbsp;String&nbsp;getContentAuthorName()</pre>
</li>
</ul>
<a name="setContentAuthorName(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContentAuthorName</h4>
<pre>public&nbsp;void&nbsp;setContentAuthorName(String&nbsp;contentAuthorName)</pre>
</li>
</ul>
<a name="getContentAuthorEmail()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentAuthorEmail</h4>
<pre>public&nbsp;String&nbsp;getContentAuthorEmail()</pre>
</li>
</ul>
<a name="setContentAuthorEmail(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContentAuthorEmail</h4>
<pre>public&nbsp;void&nbsp;setContentAuthorEmail(String&nbsp;contentAuthorEmail)</pre>
</li>
</ul>
<a name="getTitle()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitle</h4>
<pre>public&nbsp;String&nbsp;getTitle()</pre>
</li>
</ul>
<a name="setTitle(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTitle</h4>
<pre>public&nbsp;void&nbsp;setTitle(String&nbsp;title)</pre>
</li>
</ul>
<a name="getUrlContentPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUrlContentPid</h4>
<pre>public&nbsp;String&nbsp;getUrlContentPid()</pre>
</li>
</ul>
<a name="setUrlContentPid(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUrlContentPid</h4>
<pre>public&nbsp;void&nbsp;setUrlContentPid(String&nbsp;urlContentPid)</pre>
</li>
</ul>
<a name="isPreview()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPreview</h4>
<pre>public&nbsp;boolean&nbsp;isPreview()</pre>
</li>
</ul>
<a name="setPreview(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreview</h4>
<pre>public&nbsp;void&nbsp;setPreview(boolean&nbsp;preview)</pre>
</li>
</ul>
<a name="isWiki()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWiki</h4>
<pre>public&nbsp;boolean&nbsp;isWiki()</pre>
</li>
</ul>
<a name="setWiki(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWiki</h4>
<pre>public&nbsp;void&nbsp;setWiki(boolean&nbsp;wiki)</pre>
</li>
</ul>
<a name="getOfficialContentPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOfficialContentPid</h4>
<pre>public&nbsp;long&nbsp;getOfficialContentPid()</pre>
</li>
</ul>
<a name="setOfficialContentPid(long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOfficialContentPid</h4>
<pre>public&nbsp;void&nbsp;setOfficialContentPid(long&nbsp;officialContentPid)</pre>
</li>
</ul>
<a name="isAllowCommentaries()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAllowCommentaries</h4>
<pre>public&nbsp;boolean&nbsp;isAllowCommentaries()</pre>
</li>
</ul>
<a name="setAllowCommentaries(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllowCommentaries</h4>
<pre>public&nbsp;void&nbsp;setAllowCommentaries(boolean&nbsp;allowCommentaries)</pre>
</li>
</ul>
<a name="isPreModeracao()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPreModeracao</h4>
<pre>public&nbsp;boolean&nbsp;isPreModeracao()</pre>
</li>
</ul>
<a name="setPreModeracao(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreModeracao</h4>
<pre>public&nbsp;void&nbsp;setPreModeracao(boolean&nbsp;preModeracao)</pre>
</li>
</ul>
<a name="isGerarArquivoDetalhe()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isGerarArquivoDetalhe</h4>
<pre>public&nbsp;boolean&nbsp;isGerarArquivoDetalhe()</pre>
</li>
</ul>
<a name="setGerarArquivoDetalhe(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGerarArquivoDetalhe</h4>
<pre>public&nbsp;void&nbsp;setGerarArquivoDetalhe(boolean&nbsp;gerarArquivoDetalhe)</pre>
</li>
</ul>
<a name="isSerEncontradoBusca()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSerEncontradoBusca</h4>
<pre>public&nbsp;boolean&nbsp;isSerEncontradoBusca()</pre>
</li>
</ul>
<a name="setSerEncontradoBusca(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSerEncontradoBusca</h4>
<pre>public&nbsp;void&nbsp;setSerEncontradoBusca(boolean&nbsp;serEncontradoBusca)</pre>
</li>
</ul>
<a name="getImagesAnexas()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImagesAnexas</h4>
<pre>public&nbsp;List&lt;Long&gt;&nbsp;getImagesAnexas()</pre>
</li>
</ul>
<a name="setImagesAnexas(java.util.List)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setImagesAnexas</h4>
<pre>public&nbsp;void&nbsp;setImagesAnexas(List&lt;Long&gt;&nbsp;imagesAnexas)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ImportacaoImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/ImageFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/InheritComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/ImportacaoImpl.html" target="_top">Frames</a></li>
<li><a href="ImportacaoImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
