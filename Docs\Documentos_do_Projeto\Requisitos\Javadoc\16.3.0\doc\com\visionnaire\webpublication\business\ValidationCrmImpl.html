<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:22 BRT 2013 -->
<title>ValidationCrmImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="ValidationCrmImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ValidationCrmImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/UserFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/VisualContentFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/ValidationCrmImpl.html" target="_top">Frames</a></li>
<li><a href="ValidationCrmImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business</div>
<h2 title="Class ValidationCrmImpl" class="title">Class ValidationCrmImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>ValidationCrmImpl</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="strong">ValidationCrmImpl</span>
extends Object</pre>
<dl><dt><span class="strong">Author:</span></dt>
  <dd>andres</dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#dtNegociacao">dtNegociacao</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#dtVencimentoO">dtVencimentoO</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#dtVencimentoP">dtVencimentoP</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#nroNegociacao">nroNegociacao</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#tipoDebito">tipoDebito</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#ValidationCrmImpl()">ValidationCrmImpl</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#ValidationCrmImpl(int, java.lang.String, java.lang.String, java.lang.String, java.lang.String)">ValidationCrmImpl</a></strong>(int&nbsp;nroNegociacao,
                 String&nbsp;dtNegociacao,
                 String&nbsp;dtVencimentoP,
                 String&nbsp;dtVencimentoO,
                 String&nbsp;tipoDebito)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#getDtNegociacao()">getDtNegociacao</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#getDtVencimentoO()">getDtVencimentoO</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#getDtVencimentoP()">getDtVencimentoP</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#getNroNegociacao()">getNroNegociacao</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#getTipoDebito()">getTipoDebito</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#setDtNegociacao(java.lang.String)">setDtNegociacao</a></strong>(String&nbsp;dtNegociacao)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#setDtVencimentoO(java.lang.String)">setDtVencimentoO</a></strong>(String&nbsp;dtVencimentoO)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#setDtVencimentoP(java.lang.String)">setDtVencimentoP</a></strong>(String&nbsp;dtVencimentoP)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#setNroNegociacao(int)">setNroNegociacao</a></strong>(int&nbsp;nroNegociacao)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html#setTipoDebito(java.lang.String)">setTipoDebito</a></strong>(String&nbsp;tipoDebito)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="nroNegociacao">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nroNegociacao</h4>
<pre>private&nbsp;int nroNegociacao</pre>
</li>
</ul>
<a name="dtNegociacao">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dtNegociacao</h4>
<pre>private&nbsp;String dtNegociacao</pre>
</li>
</ul>
<a name="dtVencimentoP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dtVencimentoP</h4>
<pre>private&nbsp;String dtVencimentoP</pre>
</li>
</ul>
<a name="dtVencimentoO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dtVencimentoO</h4>
<pre>private&nbsp;String dtVencimentoO</pre>
</li>
</ul>
<a name="tipoDebito">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>tipoDebito</h4>
<pre>private&nbsp;String tipoDebito</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ValidationCrmImpl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ValidationCrmImpl</h4>
<pre>public&nbsp;ValidationCrmImpl()</pre>
</li>
</ul>
<a name="ValidationCrmImpl(int, java.lang.String, java.lang.String, java.lang.String, java.lang.String)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ValidationCrmImpl</h4>
<pre>public&nbsp;ValidationCrmImpl(int&nbsp;nroNegociacao,
                 String&nbsp;dtNegociacao,
                 String&nbsp;dtVencimentoP,
                 String&nbsp;dtVencimentoO,
                 String&nbsp;tipoDebito)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>tipoDebito</code> - </dd><dd><code>dtVencimento</code> - </dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getNroNegociacao()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNroNegociacao</h4>
<pre>public&nbsp;int&nbsp;getNroNegociacao()</pre>
</li>
</ul>
<a name="setNroNegociacao(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNroNegociacao</h4>
<pre>public&nbsp;void&nbsp;setNroNegociacao(int&nbsp;nroNegociacao)</pre>
</li>
</ul>
<a name="getDtNegociacao()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDtNegociacao</h4>
<pre>public&nbsp;String&nbsp;getDtNegociacao()</pre>
</li>
</ul>
<a name="setDtNegociacao(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDtNegociacao</h4>
<pre>public&nbsp;void&nbsp;setDtNegociacao(String&nbsp;dtNegociacao)</pre>
</li>
</ul>
<a name="getDtVencimentoP()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDtVencimentoP</h4>
<pre>public&nbsp;String&nbsp;getDtVencimentoP()</pre>
</li>
</ul>
<a name="setDtVencimentoP(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDtVencimentoP</h4>
<pre>public&nbsp;void&nbsp;setDtVencimentoP(String&nbsp;dtVencimentoP)</pre>
</li>
</ul>
<a name="getDtVencimentoO()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDtVencimentoO</h4>
<pre>public&nbsp;String&nbsp;getDtVencimentoO()</pre>
</li>
</ul>
<a name="setDtVencimentoO(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDtVencimentoO</h4>
<pre>public&nbsp;void&nbsp;setDtVencimentoO(String&nbsp;dtVencimentoO)</pre>
</li>
</ul>
<a name="getTipoDebito()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTipoDebito</h4>
<pre>public&nbsp;String&nbsp;getTipoDebito()</pre>
</li>
</ul>
<a name="setTipoDebito(java.lang.String)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTipoDebito</h4>
<pre>public&nbsp;void&nbsp;setTipoDebito(String&nbsp;tipoDebito)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ValidationCrmImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/UserFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/VisualContentFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/ValidationCrmImpl.html" target="_top">Frames</a></li>
<li><a href="ValidationCrmImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
