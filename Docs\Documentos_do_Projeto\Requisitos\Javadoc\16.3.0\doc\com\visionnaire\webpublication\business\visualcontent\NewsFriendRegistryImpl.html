<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:23 BRT 2013 -->
<title>NewsFriendRegistryImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="NewsFriendRegistryImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/NewsFriendRegistryImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsErrorRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html" target="_top">Frames</a></li>
<li><a href="NewsFriendRegistryImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent</div>
<h2 title="Class NewsFriendRegistryImpl" class="title">Class NewsFriendRegistryImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>BusinessObjectImpl</li>
<li>
<ul class="inheritance">
<li>NewsFriendRegistryImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent, DependenceOnVisualContent, NewsRegistryDestinatary, NewsRegistrySender</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">NewsFriendRegistryImpl</span>
extends BusinessObjectImpl
implements DependenceOnVisualContent, NewsRegistrySender, NewsRegistryDestinatary</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantNewsFriendRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss">PServantNewsFriendRegistryImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#ps">ps</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#NewsFriendRegistryImpl(com.visionnaire.PSS.pid)">NewsFriendRegistryImpl</a></strong>(pid&nbsp;oid)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#NewsFriendRegistryImpl(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.util.Date, com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">NewsFriendRegistryImpl</a></strong>(String&nbsp;sender,
                      String&nbsp;sender_email,
                      String&nbsp;destinatary,
                      String&nbsp;destinatary_email,
                      String&nbsp;text,
                      String&nbsp;sender_ip,
                      Date&nbsp;send_date,
                      <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;visual_content)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#delete()">delete</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getDestinatary_email()">getDestinatary_email</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getDestinataryName()">getDestinataryName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getPidObject()">getPidObject</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getSend_date()">getSend_date</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getSender_email()">getSender_email</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getSender_ip()">getSender_ip</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getSenderName()">getSenderName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getStringDate()">getStringDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getText()">getText</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#getVisualContent()">getVisualContent</a></strong>()</code>
<div class="block">Retorna o visual content associado a quem implementar a inerface</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#pssDeleteFields()">pssDeleteFields</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#setDestinatary_email(java.lang.String)">setDestinatary_email</a></strong>(String&nbsp;destinatary_email)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#setDestinatary(java.lang.String)">setDestinatary</a></strong>(String&nbsp;destinatary)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#setSend_date(java.util.Date)">setSend_date</a></strong>(Date&nbsp;send_date)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#setSender_email(java.lang.String)">setSender_email</a></strong>(String&nbsp;sender_email)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#setSender_ip(java.lang.String)">setSender_ip</a></strong>(String&nbsp;sender_ip)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#setSender(java.lang.String)">setSender</a></strong>(String&nbsp;sender)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#setText(java.lang.String)">setText</a></strong>(String&nbsp;text)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html#setVisual_content(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">setVisual_content</a></strong>(<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;visual_content)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_BusinessObjectImpl">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;BusinessObjectImpl</h3>
<code>getPid, pssServant, setPS</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, getClassId, hashCode, isDying, pssDelete, pssDestroy, pssLock, toInternal, toInternal, toString, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ps">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ps</h4>
<pre>private final&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantNewsFriendRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss">PServantNewsFriendRegistryImpl</a> ps</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="NewsFriendRegistryImpl(com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NewsFriendRegistryImpl</h4>
<pre>public&nbsp;NewsFriendRegistryImpl(pid&nbsp;oid)</pre>
</li>
</ul>
<a name="NewsFriendRegistryImpl(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.util.Date, com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NewsFriendRegistryImpl</h4>
<pre>public&nbsp;NewsFriendRegistryImpl(String&nbsp;sender,
                      String&nbsp;sender_email,
                      String&nbsp;destinatary,
                      String&nbsp;destinatary_email,
                      String&nbsp;text,
                      String&nbsp;sender_ip,
                      Date&nbsp;send_date,
                      <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;visual_content)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssDeleteFields()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssDeleteFields</h4>
<pre>public&nbsp;void&nbsp;pssDeleteFields()
                     throws NotDisconnectedException</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in class&nbsp;<code>PersistentImplBase</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>NotDisconnectedException</code></dd></dl>
</li>
</ul>
<a name="getSenderName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSenderName</h4>
<pre>public&nbsp;String&nbsp;getSenderName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getSenderName</code>&nbsp;in interface&nbsp;<code>NewsRegistrySender</code></dd>
<dt><span class="strong">Returns:</span></dt><dd></dd></dl>
</li>
</ul>
<a name="setSender(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSender</h4>
<pre>public&nbsp;void&nbsp;setSender(String&nbsp;sender)</pre>
</li>
</ul>
<a name="getSender_email()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSender_email</h4>
<pre>public&nbsp;String&nbsp;getSender_email()</pre>
</li>
</ul>
<a name="setSender_email(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSender_email</h4>
<pre>public&nbsp;void&nbsp;setSender_email(String&nbsp;sender_email)</pre>
</li>
</ul>
<a name="getDestinataryName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinataryName</h4>
<pre>public&nbsp;String&nbsp;getDestinataryName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getDestinataryName</code>&nbsp;in interface&nbsp;<code>NewsRegistryDestinatary</code></dd>
</dl>
</li>
</ul>
<a name="setDestinatary(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDestinatary</h4>
<pre>public&nbsp;void&nbsp;setDestinatary(String&nbsp;destinatary)</pre>
</li>
</ul>
<a name="getDestinatary_email()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinatary_email</h4>
<pre>public&nbsp;String&nbsp;getDestinatary_email()</pre>
</li>
</ul>
<a name="setDestinatary_email(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDestinatary_email</h4>
<pre>public&nbsp;void&nbsp;setDestinatary_email(String&nbsp;destinatary_email)</pre>
</li>
</ul>
<a name="getText()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getText</h4>
<pre>public&nbsp;String&nbsp;getText()</pre>
</li>
</ul>
<a name="setText(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setText</h4>
<pre>public&nbsp;void&nbsp;setText(String&nbsp;text)</pre>
</li>
</ul>
<a name="getSender_ip()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSender_ip</h4>
<pre>public&nbsp;String&nbsp;getSender_ip()</pre>
</li>
</ul>
<a name="setSender_ip(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSender_ip</h4>
<pre>public&nbsp;void&nbsp;setSender_ip(String&nbsp;sender_ip)</pre>
</li>
</ul>
<a name="getSend_date()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSend_date</h4>
<pre>public&nbsp;Date&nbsp;getSend_date()</pre>
</li>
</ul>
<a name="setSend_date(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSend_date</h4>
<pre>public&nbsp;void&nbsp;setSend_date(Date&nbsp;send_date)</pre>
</li>
</ul>
<a name="getVisualContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVisualContent</h4>
<pre>public&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;getVisualContent()</pre>
<div class="block"><strong>Description copied from interface:&nbsp;<code>com.visionnaire.webpublication.business.visualcontent.contratcts.DependenceOnVisualContent</code></strong></div>
<div class="block">Retorna o visual content associado a quem implementar a inerface</div>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getVisualContent</code>&nbsp;in interface&nbsp;<code>DependenceOnVisualContent</code></dd>
<dt><span class="strong">Returns:</span></dt><dd>VisualContentImpl</dd></dl>
</li>
</ul>
<a name="setVisual_content(com.visionnaire.webpublication.business.visualcontent.VisualContentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisual_content</h4>
<pre>public&nbsp;void&nbsp;setVisual_content(<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&nbsp;visual_content)</pre>
</li>
</ul>
<a name="getStringDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStringDate</h4>
<pre>public&nbsp;String&nbsp;getStringDate()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String date</dd></dl>
</li>
</ul>
<a name="getPidObject()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPidObject</h4>
<pre>public&nbsp;long&nbsp;getPidObject()</pre>
</li>
</ul>
<a name="delete()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>delete</h4>
<pre>public&nbsp;void&nbsp;delete()
            throws <a href="../../../../../com/visionnaire/webpublication/business/exception/BusinessException.html" title="class in com.visionnaire.webpublication.business.exception">BusinessException</a></pre>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../../../com/visionnaire/webpublication/business/exception/BusinessException.html" title="class in com.visionnaire.webpublication.business.exception">BusinessException</a></code></dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/NewsFriendRegistryImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsErrorRegistryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/NewsImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/NewsFriendRegistryImpl.html" target="_top">Frames</a></li>
<li><a href="NewsFriendRegistryImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
