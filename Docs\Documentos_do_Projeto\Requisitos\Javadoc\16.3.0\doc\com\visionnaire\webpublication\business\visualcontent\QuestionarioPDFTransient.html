<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:23 BRT 2013 -->
<title>QuestionarioPDFTransient</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="QuestionarioPDFTransient";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/QuestionarioPDFTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ProductServiceImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RelatedLinkImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html" target="_top">Frames</a></li>
<li><a href="QuestionarioPDFTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent</div>
<h2 title="Class QuestionarioPDFTransient" class="title">Class QuestionarioPDFTransient</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>QuestionarioPDFTransient</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="strong">QuestionarioPDFTransient</span>
extends Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#pid">pid</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private List&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">RespostaPDFTransient</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#respostas">respostas</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#titulo">titulo</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#QuestionarioPDFTransient()">QuestionarioPDFTransient</a></strong>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#getPid()">getPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>List&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">RespostaPDFTransient</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#getRespostas()">getRespostas</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#getTitulo()">getTitulo</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#setPid(long)">setPid</a></strong>(long&nbsp;pid)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#setRespostas(com.visionnaire.webpublication.business.visualcontent.RespostaPDFTransient)">setRespostas</a></strong>(<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">RespostaPDFTransient</a>&nbsp;resposta)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html#setTitulo(java.lang.String)">setTitulo</a></strong>(String&nbsp;titulo)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="pid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pid</h4>
<pre>private&nbsp;long pid</pre>
</li>
</ul>
<a name="titulo">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>titulo</h4>
<pre>private&nbsp;String titulo</pre>
</li>
</ul>
<a name="respostas">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>respostas</h4>
<pre>private&nbsp;List&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">RespostaPDFTransient</a>&gt; respostas</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="QuestionarioPDFTransient()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>QuestionarioPDFTransient</h4>
<pre>public&nbsp;QuestionarioPDFTransient()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPid</h4>
<pre>public&nbsp;long&nbsp;getPid()</pre>
</li>
</ul>
<a name="setPid(long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPid</h4>
<pre>public&nbsp;void&nbsp;setPid(long&nbsp;pid)</pre>
</li>
</ul>
<a name="getTitulo()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitulo</h4>
<pre>public&nbsp;String&nbsp;getTitulo()</pre>
</li>
</ul>
<a name="setTitulo(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTitulo</h4>
<pre>public&nbsp;void&nbsp;setTitulo(String&nbsp;titulo)</pre>
</li>
</ul>
<a name="getRespostas()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRespostas</h4>
<pre>public&nbsp;List&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">RespostaPDFTransient</a>&gt;&nbsp;getRespostas()</pre>
</li>
</ul>
<a name="setRespostas(com.visionnaire.webpublication.business.visualcontent.RespostaPDFTransient)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setRespostas</h4>
<pre>public&nbsp;void&nbsp;setRespostas(<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">RespostaPDFTransient</a>&nbsp;resposta)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/QuestionarioPDFTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ProductServiceImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RelatedLinkImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/QuestionarioPDFTransient.html" target="_top">Frames</a></li>
<li><a href="QuestionarioPDFTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
