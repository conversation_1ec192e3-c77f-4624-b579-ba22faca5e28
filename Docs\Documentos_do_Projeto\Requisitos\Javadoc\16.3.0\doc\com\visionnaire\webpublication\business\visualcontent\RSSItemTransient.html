<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:23 BRT 2013 -->
<title>RSSItemTransient</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="RSSItemTransient";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RSSItemTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSReaderImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html" target="_top">Frames</a></li>
<li><a href="RSSItemTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent</div>
<h2 title="Class RSSItemTransient" class="title">Class RSSItemTransient</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>RSSItemTransient</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">RSSItemTransient</span>
extends Object
implements Serializable</pre>
<dl><dt><span class="strong">Author:</span></dt>
  <dd>danilo</dd>
<dt><span class="strong">See Also:</span></dt><dd><a href="../../../../../serialized-form.html#com.visionnaire.webpublication.business.visualcontent.RSSItemTransient">Serialized Form</a></dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private char</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#checked">checked</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#nameitem">nameitem</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#pid">pid</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#rssreader">rssreader</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private static long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#serialVersionUID">serialVersionUID</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#tagitem">tagitem</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#typeitem">typeitem</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#RSSItemTransient(long, java.lang.String, java.lang.String, int, char, long)">RSSItemTransient</a></strong>(long&nbsp;_pid,
                String&nbsp;_nameitem,
                String&nbsp;_tagitem,
                int&nbsp;_typeitem,
                char&nbsp;_checked,
                long&nbsp;_rssreader)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#getChecked()">getChecked</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#getNameitem()">getNameitem</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#getPid()">getPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#getRssReader()">getRssReader</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#getTagitem()">getTagitem</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#getTypeitem()">getTypeitem</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#setChecked(char)">setChecked</a></strong>(char&nbsp;_checked)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#setNameitem(java.lang.String)">setNameitem</a></strong>(String&nbsp;_nameitem)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#setPid(long)">setPid</a></strong>(long&nbsp;_pid)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#setRssReader(long)">setRssReader</a></strong>(long&nbsp;_rssreader)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#setTagitem(java.lang.String)">setTagitem</a></strong>(String&nbsp;_tagitem)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#setTypeitem(int)">setTypeitem</a></strong>(int&nbsp;_typeitem)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">RSSItemTransient</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html#toTransient()">toTransient</a></strong>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="serialVersionUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serialVersionUID</h4>
<pre>private static final&nbsp;long serialVersionUID</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../../constant-values.html#com.visionnaire.webpublication.business.visualcontent.RSSItemTransient.serialVersionUID">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="pid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pid</h4>
<pre>private&nbsp;long pid</pre>
</li>
</ul>
<a name="nameitem">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nameitem</h4>
<pre>private&nbsp;String nameitem</pre>
</li>
</ul>
<a name="tagitem">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tagitem</h4>
<pre>private&nbsp;String tagitem</pre>
</li>
</ul>
<a name="typeitem">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>typeitem</h4>
<pre>private&nbsp;int typeitem</pre>
</li>
</ul>
<a name="checked">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checked</h4>
<pre>private&nbsp;char checked</pre>
</li>
</ul>
<a name="rssreader">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>rssreader</h4>
<pre>private&nbsp;long rssreader</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RSSItemTransient(long, java.lang.String, java.lang.String, int, char, long)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RSSItemTransient</h4>
<pre>public&nbsp;RSSItemTransient(long&nbsp;_pid,
                String&nbsp;_nameitem,
                String&nbsp;_tagitem,
                int&nbsp;_typeitem,
                char&nbsp;_checked,
                long&nbsp;_rssreader)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_pid</code> - </dd><dd><code>_nameitem</code> - </dd><dd><code>_tagitem</code> - </dd><dd><code>_typeitem</code> - </dd><dd><code>_checked</code> - </dd><dd><code>_rssreader</code> - </dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPid</h4>
<pre>public&nbsp;long&nbsp;getPid()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>long</dd></dl>
</li>
</ul>
<a name="setPid(long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPid</h4>
<pre>public&nbsp;void&nbsp;setPid(long&nbsp;_pid)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_pid</code> - </dd></dl>
</li>
</ul>
<a name="getNameitem()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameitem</h4>
<pre>public&nbsp;String&nbsp;getNameitem()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setNameitem(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameitem</h4>
<pre>public&nbsp;void&nbsp;setNameitem(String&nbsp;_nameitem)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_nameitem</code> - </dd></dl>
</li>
</ul>
<a name="getTagitem()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTagitem</h4>
<pre>public&nbsp;String&nbsp;getTagitem()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setTagitem(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTagitem</h4>
<pre>public&nbsp;void&nbsp;setTagitem(String&nbsp;_tagitem)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_tagitem</code> - </dd></dl>
</li>
</ul>
<a name="getRssReader()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRssReader</h4>
<pre>public&nbsp;long&nbsp;getRssReader()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>long</dd></dl>
</li>
</ul>
<a name="setRssReader(long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRssReader</h4>
<pre>public&nbsp;void&nbsp;setRssReader(long&nbsp;_rssreader)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_rssreader</code> - </dd></dl>
</li>
</ul>
<a name="getTypeitem()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTypeitem</h4>
<pre>public&nbsp;int&nbsp;getTypeitem()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="setTypeitem(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTypeitem</h4>
<pre>public&nbsp;void&nbsp;setTypeitem(int&nbsp;_typeitem)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_typeitem</code> - </dd></dl>
</li>
</ul>
<a name="getChecked()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChecked</h4>
<pre>public&nbsp;char&nbsp;getChecked()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>char</dd></dl>
</li>
</ul>
<a name="setChecked(char)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setChecked</h4>
<pre>public&nbsp;void&nbsp;setChecked(char&nbsp;_checked)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>_checked</code> - </dd></dl>
</li>
</ul>
<a name="toTransient()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toTransient</h4>
<pre>public&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent">RSSItemTransient</a>&nbsp;toTransient()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>ResearchQuestionTransient</dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RSSItemTransient.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSItemImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RSSReaderImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/RSSItemTransient.html" target="_top">Frames</a></li>
<li><a href="RSSItemTransient.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
