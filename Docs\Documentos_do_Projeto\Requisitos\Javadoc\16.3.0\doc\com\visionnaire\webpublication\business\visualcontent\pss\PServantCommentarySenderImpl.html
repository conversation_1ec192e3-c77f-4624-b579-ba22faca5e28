<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:24 BRT 2013 -->
<title>PServantCommentarySenderImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantCommentarySenderImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantCommentarySenderImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantContactImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html" target="_top">Frames</a></li>
<li><a href="PServantCommentarySenderImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent.pss</div>
<h2 title="Class PServantCommentarySenderImpl" class="title">Class PServantCommentarySenderImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantCommentarySenderImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantCommentarySenderImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#cityauthor__v">cityauthor__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#commentaries__q">commentaries__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#companyauthor__v">companyauthor__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#email__v">email__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#ipauthor__v">ipauthor__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#name__v">name__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#receivenews__v">receivenews__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#siteauthor__v">siteauthor__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#stateauthor__v">stateauthor__v</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#PServantCommentarySenderImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantCommentarySenderImpl</a></strong>(Persistent&nbsp;jobj,
                            pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#PServantCommentarySenderImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantCommentarySenderImpl</a></strong>(pid&nbsp;id,
                            Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#cityauthor()">cityauthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#cityauthor(java.lang.String)">cityauthor</a></strong>(String&nbsp;cityauthor)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#commentaries()">commentaries</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#commentariesCount()">commentariesCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#companyauthor()">companyauthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#companyauthor(java.lang.String)">companyauthor</a></strong>(String&nbsp;companyauthor)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#email()">email</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#email(java.lang.String)">email</a></strong>(String&nbsp;email)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#ipauthor()">ipauthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#ipauthor(java.lang.String)">ipauthor</a></strong>(String&nbsp;ipauthor)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#name()">name</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#name(java.lang.String)">name</a></strong>(String&nbsp;name)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#receivenews()">receivenews</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#receivenews(boolean)">receivenews</a></strong>(boolean&nbsp;receivenews)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#siteauthor()">siteauthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#siteauthor(java.lang.String)">siteauthor</a></strong>(String&nbsp;siteauthor)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#stateauthor()">stateauthor</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html#stateauthor(java.lang.String)">stateauthor</a></strong>(String&nbsp;stateauthor)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="name__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name__v</h4>
<pre>private&nbsp;String name__v</pre>
</li>
</ul>
<a name="email__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>email__v</h4>
<pre>private&nbsp;String email__v</pre>
</li>
</ul>
<a name="siteauthor__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>siteauthor__v</h4>
<pre>private&nbsp;String siteauthor__v</pre>
</li>
</ul>
<a name="cityauthor__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cityauthor__v</h4>
<pre>private&nbsp;String cityauthor__v</pre>
</li>
</ul>
<a name="stateauthor__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stateauthor__v</h4>
<pre>private&nbsp;String stateauthor__v</pre>
</li>
</ul>
<a name="ipauthor__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ipauthor__v</h4>
<pre>private&nbsp;String ipauthor__v</pre>
</li>
</ul>
<a name="companyauthor__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>companyauthor__v</h4>
<pre>private&nbsp;String companyauthor__v</pre>
</li>
</ul>
<a name="receivenews__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>receivenews__v</h4>
<pre>private&nbsp;boolean receivenews__v</pre>
</li>
</ul>
<a name="commentaries__q">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>commentaries__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt; commentaries__q</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantCommentarySenderImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantCommentarySenderImpl</h4>
<pre>public&nbsp;PServantCommentarySenderImpl(pid&nbsp;id,
                            Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantCommentarySenderImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantCommentarySenderImpl</h4>
<pre>public&nbsp;PServantCommentarySenderImpl(Persistent&nbsp;jobj,
                            pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="name()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>public final&nbsp;String&nbsp;name()</pre>
</li>
</ul>
<a name="name(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>public final&nbsp;void&nbsp;name(String&nbsp;name)</pre>
</li>
</ul>
<a name="email()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>email</h4>
<pre>public final&nbsp;String&nbsp;email()</pre>
</li>
</ul>
<a name="email(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>email</h4>
<pre>public final&nbsp;void&nbsp;email(String&nbsp;email)</pre>
</li>
</ul>
<a name="siteauthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>siteauthor</h4>
<pre>public final&nbsp;String&nbsp;siteauthor()</pre>
</li>
</ul>
<a name="siteauthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>siteauthor</h4>
<pre>public final&nbsp;void&nbsp;siteauthor(String&nbsp;siteauthor)</pre>
</li>
</ul>
<a name="cityauthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cityauthor</h4>
<pre>public final&nbsp;String&nbsp;cityauthor()</pre>
</li>
</ul>
<a name="cityauthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cityauthor</h4>
<pre>public final&nbsp;void&nbsp;cityauthor(String&nbsp;cityauthor)</pre>
</li>
</ul>
<a name="stateauthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stateauthor</h4>
<pre>public final&nbsp;String&nbsp;stateauthor()</pre>
</li>
</ul>
<a name="stateauthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stateauthor</h4>
<pre>public final&nbsp;void&nbsp;stateauthor(String&nbsp;stateauthor)</pre>
</li>
</ul>
<a name="ipauthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ipauthor</h4>
<pre>public final&nbsp;String&nbsp;ipauthor()</pre>
</li>
</ul>
<a name="ipauthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ipauthor</h4>
<pre>public final&nbsp;void&nbsp;ipauthor(String&nbsp;ipauthor)</pre>
</li>
</ul>
<a name="companyauthor()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>companyauthor</h4>
<pre>public final&nbsp;String&nbsp;companyauthor()</pre>
</li>
</ul>
<a name="companyauthor(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>companyauthor</h4>
<pre>public final&nbsp;void&nbsp;companyauthor(String&nbsp;companyauthor)</pre>
</li>
</ul>
<a name="receivenews()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>receivenews</h4>
<pre>public final&nbsp;boolean&nbsp;receivenews()</pre>
</li>
</ul>
<a name="receivenews(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>receivenews</h4>
<pre>public final&nbsp;void&nbsp;receivenews(boolean&nbsp;receivenews)</pre>
</li>
</ul>
<a name="commentaries()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentaries</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/CommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">CommentaryImpl</a>&gt;&nbsp;commentaries()</pre>
</li>
</ul>
<a name="commentariesCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentariesCount</h4>
<pre>public final&nbsp;int&nbsp;commentariesCount()</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantCommentarySenderImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentaryImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantContactImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantCommentarySenderImpl.html" target="_top">Frames</a></li>
<li><a href="PServantCommentarySenderImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
