<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:21 BRT 2013 -->
<title>Constants</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Constants";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Constants.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/ContentStateImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/Constants.html" target="_top">Frames</a></li>
<li><a href="Constants.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods_inherited_from_class_Object">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business</div>
<h2 title="Class Constants" class="title">Class Constants</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>Constants</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="strong">Constants</span>
extends Object</pre>
<div class="block">Insert the type's description here. Creation date: (2/5/2002 18:04:46)
 Atualizado em 28/11/2007 - Danilo</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#ANALISTA">ANALISTA</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#AUTHENTICATION">AUTHENTICATION</a></strong></code>
<div class="block">Constantes dos Componentes Visuais</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#CONTACT">CONTACT</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#CONTATOCRM">CONTATOCRM</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#DICAS">DICAS</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#ESPECIALIDADEMEDICOCRM">ESPECIALIDADEMEDICOCRM</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#EVENT_AGENDA">EVENT_AGENDA</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#FAQ">FAQ</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#FORMACAOCRM">FORMACAOCRM</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#FREE_COMPONENT">FREE_COMPONENT</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#NEWS">NEWS</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#NEWSLETTER">NEWSLETTER</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#NOTES">NOTES</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#POLL">POLL</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#PRODUCT_SERVICE">PRODUCT_SERVICE</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#RESEARCH">RESEARCH</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#RSSREADER">RSSREADER</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#SEARCH">SEARCH</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static String</code></td>
<td class="colLast"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#THUMBS">THUMBS</a></strong></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../com/visionnaire/webpublication/business/Constants.html#Constants()">Constants</a></strong>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="AUTHENTICATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AUTHENTICATION</h4>
<pre>public static final&nbsp;String AUTHENTICATION</pre>
<div class="block">Constantes dos Componentes Visuais</div>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.AUTHENTICATION">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="EVENT_AGENDA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EVENT_AGENDA</h4>
<pre>public static final&nbsp;String EVENT_AGENDA</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.EVENT_AGENDA">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="FREE_COMPONENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FREE_COMPONENT</h4>
<pre>public static final&nbsp;String FREE_COMPONENT</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.FREE_COMPONENT">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="ANALISTA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ANALISTA</h4>
<pre>public static final&nbsp;String ANALISTA</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.ANALISTA">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="NEWS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEWS</h4>
<pre>public static final&nbsp;String NEWS</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.NEWS">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="POLL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>POLL</h4>
<pre>public static final&nbsp;String POLL</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.POLL">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="SEARCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SEARCH</h4>
<pre>public static final&nbsp;String SEARCH</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.SEARCH">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="RESEARCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESEARCH</h4>
<pre>public static final&nbsp;String RESEARCH</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.RESEARCH">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="THUMBS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>THUMBS</h4>
<pre>public static final&nbsp;String THUMBS</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.THUMBS">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="DICAS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DICAS</h4>
<pre>public static final&nbsp;String DICAS</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.DICAS">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="PRODUCT_SERVICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRODUCT_SERVICE</h4>
<pre>public static final&nbsp;String PRODUCT_SERVICE</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.PRODUCT_SERVICE">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="FAQ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FAQ</h4>
<pre>public static final&nbsp;String FAQ</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.FAQ">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="NOTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOTES</h4>
<pre>public static final&nbsp;String NOTES</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.NOTES">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="CONTACT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CONTACT</h4>
<pre>public static final&nbsp;String CONTACT</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.CONTACT">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="NEWSLETTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEWSLETTER</h4>
<pre>public static final&nbsp;String NEWSLETTER</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.NEWSLETTER">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="RSSREADER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RSSREADER</h4>
<pre>public static final&nbsp;String RSSREADER</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.RSSREADER">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="FORMACAOCRM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FORMACAOCRM</h4>
<pre>public static final&nbsp;String FORMACAOCRM</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.FORMACAOCRM">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="CONTATOCRM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CONTATOCRM</h4>
<pre>public static final&nbsp;String CONTATOCRM</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.CONTATOCRM">Constant Field Values</a></dd></dl>
</li>
</ul>
<a name="ESPECIALIDADEMEDICOCRM">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ESPECIALIDADEMEDICOCRM</h4>
<pre>public static final&nbsp;String ESPECIALIDADEMEDICOCRM</pre>
<dl><dt><span class="strong">See Also:</span></dt><dd><a href="../../../../constant-values.html#com.visionnaire.webpublication.business.Constants.ESPECIALIDADEMEDICOCRM">Constant Field Values</a></dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Constants()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Constants</h4>
<pre>public&nbsp;Constants()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Constants.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/ContentStateImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/Constants.html" target="_top">Frames</a></li>
<li><a href="Constants.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods_inherited_from_class_Object">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
