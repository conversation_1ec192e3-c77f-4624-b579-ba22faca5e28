<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:22 BRT 2013 -->
<title>PServantComponentImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantComponentImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantComponentImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev Class</li>
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantContentStateImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/pss/PServantComponentImpl.html" target="_top">Frames</a></li>
<li><a href="PServantComponentImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.pss</div>
<h2 title="Class PServantComponentImpl" class="title">Class PServantComponentImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantComponentImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantComponentImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private char</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#active__v">active__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#captcha__v">captcha__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#childrenComponents__q">childrenComponents__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#column__v">column__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#componentTitle__v">componentTitle__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#contents__q">contents__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#emailResponsavel__v">emailResponsavel__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#fatherComponentPid__k">fatherComponentPid__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#fatherComponentPid__o">fatherComponentPid__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#fatherComponentPid__v">fatherComponentPid__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#flagFriendlyUrl__v">flagFriendlyUrl__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#gerararquivos__v">gerararquivos__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;GrantedSiteImpl&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#grantedSites__q">grantedSites__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#groupNames__q">groupNames__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#imageMiniHeight__v">imageMiniHeight__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#imageMiniWidth__v">imageMiniWidth__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/InheritComponentImpl.html" title="class in com.visionnaire.webpublication.business">InheritComponentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#inheritComponents__q">inheritComponents__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#maximumContent__v">maximumContent__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#name__v">name__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#netUserCreateContent__v">netUserCreateContent__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#netUserUpdateContent__v">netUserUpdateContent__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#nickname__v">nickname__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#order__v">order__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pagExibirGrupos__v">pagExibirGrupos__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#paging__v">paging__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pagNrItensPorPagina__v">pagNrItensPorPagina__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pagOrdenacao__v">pagOrdenacao__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/ParameterComponentImpl.html" title="class in com.visionnaire.webpublication.business">ParameterComponentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#parameters__q">parameters__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#protection__v">protection__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#serencontradobusca__v">serencontradobusca__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#site__k">site__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#site__o">site__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#site__v">site__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#template__v">template__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#type__v">type__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#visible__v">visible__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#wiki__v">wiki__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#workflow__k">workflow__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#workflow__o">workflow__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#workflow__v">workflow__v</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#PServantComponentImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantComponentImpl</a></strong>(Persistent&nbsp;jobj,
                     pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#PServantComponentImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantComponentImpl</a></strong>(pid&nbsp;id,
                     Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#active()">active</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#active(char)">active</a></strong>(char&nbsp;active)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#captcha()">captcha</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#captcha(boolean)">captcha</a></strong>(boolean&nbsp;captcha)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#childrenComponents()">childrenComponents</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#childrenComponentsCount()">childrenComponentsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#column()">column</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#column(int)">column</a></strong>(int&nbsp;column)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#componentTitle()">componentTitle</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#componentTitle(java.lang.String)">componentTitle</a></strong>(String&nbsp;componentTitle)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#contents()">contents</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#contentsCount()">contentsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#emailResponsavel()">emailResponsavel</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#emailResponsavel(java.lang.String)">emailResponsavel</a></strong>(String&nbsp;emailResponsavel)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#fatherComponentPid()">fatherComponentPid</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#fatherComponentPid(com.visionnaire.webpublication.business.ComponentImpl)">fatherComponentPid</a></strong>(<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;fatherComponentPid)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#flagFriendlyUrl()">flagFriendlyUrl</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#flagFriendlyUrl(boolean)">flagFriendlyUrl</a></strong>(boolean&nbsp;flagFriendlyUrl)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#gerararquivos()">gerararquivos</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#gerararquivos(boolean)">gerararquivos</a></strong>(boolean&nbsp;gerararquivos)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;GrantedSiteImpl&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#grantedSites()">grantedSites</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#grantedSites(com.visionnaire.PSS.client.PList)">grantedSites</a></strong>(PList&lt;GrantedSiteImpl&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#grantedSitesCount()">grantedSitesCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#grantedSitesInsert(com.visionnaire.webpublication.access.GrantedSiteImpl)">grantedSitesInsert</a></strong>(GrantedSiteImpl&nbsp;grantedSite)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#grantedSitesRemove(com.visionnaire.webpublication.access.GrantedSiteImpl)">grantedSitesRemove</a></strong>(GrantedSiteImpl&nbsp;grantedSite)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#groupNames()">groupNames</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#groupNamesCount()">groupNamesCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#imageMiniHeight()">imageMiniHeight</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#imageMiniHeight(int)">imageMiniHeight</a></strong>(int&nbsp;imageMiniHeight)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#imageMiniWidth()">imageMiniWidth</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#imageMiniWidth(int)">imageMiniWidth</a></strong>(int&nbsp;imageMiniWidth)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/InheritComponentImpl.html" title="class in com.visionnaire.webpublication.business">InheritComponentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#inheritComponents()">inheritComponents</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#inheritComponentsCount()">inheritComponentsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#maximumContent()">maximumContent</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#maximumContent(int)">maximumContent</a></strong>(int&nbsp;maximumContent)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#name()">name</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#name(java.lang.String)">name</a></strong>(String&nbsp;name)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#netUserCreateContent()">netUserCreateContent</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#netUserCreateContent(boolean)">netUserCreateContent</a></strong>(boolean&nbsp;netUserCreateContent)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#netUserUpdateContent()">netUserUpdateContent</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#netUserUpdateContent(boolean)">netUserUpdateContent</a></strong>(boolean&nbsp;netUserUpdateContent)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#nickname()">nickname</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#nickname(java.lang.String)">nickname</a></strong>(String&nbsp;nickname)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#order()">order</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#order(int)">order</a></strong>(int&nbsp;order)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pagExibirGrupos()">pagExibirGrupos</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pagExibirGrupos(boolean)">pagExibirGrupos</a></strong>(boolean&nbsp;pagExibirGrupos)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#paging()">paging</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#paging(int)">paging</a></strong>(int&nbsp;paging)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pagNrItensPorPagina()">pagNrItensPorPagina</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pagNrItensPorPagina(int)">pagNrItensPorPagina</a></strong>(int&nbsp;pagNrItensPorPagina)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pagOrdenacao()">pagOrdenacao</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pagOrdenacao(int)">pagOrdenacao</a></strong>(int&nbsp;pagOrdenacao)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/ParameterComponentImpl.html" title="class in com.visionnaire.webpublication.business">ParameterComponentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#parameters()">parameters</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#parametersCount()">parametersCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#protection()">protection</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#protection(int)">protection</a></strong>(int&nbsp;protection)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#serencontradobusca()">serencontradobusca</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#serencontradobusca(boolean)">serencontradobusca</a></strong>(boolean&nbsp;serencontradobusca)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#site()">site</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#site(com.visionnaire.webpublication.business.SiteImpl)">site</a></strong>(<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;site)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#template()">template</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#template(java.lang.String)">template</a></strong>(String&nbsp;template)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#type()">type</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#type(java.lang.String)">type</a></strong>(String&nbsp;type)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#visible()">visible</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#visible(boolean)">visible</a></strong>(boolean&nbsp;visible)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#wiki()">wiki</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#wiki(boolean)">wiki</a></strong>(boolean&nbsp;wiki)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#workflow()">workflow</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantComponentImpl.html#workflow(com.visionnaire.webpublication.workflow.WorkFlowImpl)">workflow</a></strong>(<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&nbsp;workflow)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="componentTitle__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>componentTitle__v</h4>
<pre>private&nbsp;String componentTitle__v</pre>
</li>
</ul>
<a name="name__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name__v</h4>
<pre>private&nbsp;String name__v</pre>
</li>
</ul>
<a name="column__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>column__v</h4>
<pre>private&nbsp;int column__v</pre>
</li>
</ul>
<a name="order__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>order__v</h4>
<pre>private&nbsp;int order__v</pre>
</li>
</ul>
<a name="active__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>active__v</h4>
<pre>private&nbsp;char active__v</pre>
</li>
</ul>
<a name="visible__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>visible__v</h4>
<pre>private&nbsp;boolean visible__v</pre>
</li>
</ul>
<a name="paging__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>paging__v</h4>
<pre>private&nbsp;int paging__v</pre>
</li>
</ul>
<a name="template__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>template__v</h4>
<pre>private&nbsp;String template__v</pre>
</li>
</ul>
<a name="maximumContent__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>maximumContent__v</h4>
<pre>private&nbsp;int maximumContent__v</pre>
</li>
</ul>
<a name="protection__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>protection__v</h4>
<pre>private&nbsp;int protection__v</pre>
</li>
</ul>
<a name="type__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type__v</h4>
<pre>private&nbsp;String type__v</pre>
</li>
</ul>
<a name="imageMiniHeight__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imageMiniHeight__v</h4>
<pre>private&nbsp;int imageMiniHeight__v</pre>
</li>
</ul>
<a name="imageMiniWidth__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imageMiniWidth__v</h4>
<pre>private&nbsp;int imageMiniWidth__v</pre>
</li>
</ul>
<a name="emailResponsavel__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emailResponsavel__v</h4>
<pre>private&nbsp;String emailResponsavel__v</pre>
</li>
</ul>
<a name="netUserCreateContent__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>netUserCreateContent__v</h4>
<pre>private&nbsp;boolean netUserCreateContent__v</pre>
</li>
</ul>
<a name="netUserUpdateContent__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>netUserUpdateContent__v</h4>
<pre>private&nbsp;boolean netUserUpdateContent__v</pre>
</li>
</ul>
<a name="wiki__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wiki__v</h4>
<pre>private&nbsp;boolean wiki__v</pre>
</li>
</ul>
<a name="pagNrItensPorPagina__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagNrItensPorPagina__v</h4>
<pre>private&nbsp;int pagNrItensPorPagina__v</pre>
</li>
</ul>
<a name="pagOrdenacao__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagOrdenacao__v</h4>
<pre>private&nbsp;int pagOrdenacao__v</pre>
</li>
</ul>
<a name="pagExibirGrupos__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagExibirGrupos__v</h4>
<pre>private&nbsp;boolean pagExibirGrupos__v</pre>
</li>
</ul>
<a name="flagFriendlyUrl__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flagFriendlyUrl__v</h4>
<pre>private&nbsp;boolean flagFriendlyUrl__v</pre>
</li>
</ul>
<a name="site__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>site__o</h4>
<pre>private&nbsp;pid site__o</pre>
</li>
</ul>
<a name="site__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>site__k</h4>
<pre>private&nbsp;pid site__k</pre>
</li>
</ul>
<a name="site__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>site__v</h4>
<pre>private&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a> site__v</pre>
</li>
</ul>
<a name="workflow__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflow__o</h4>
<pre>private&nbsp;pid workflow__o</pre>
</li>
</ul>
<a name="workflow__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflow__k</h4>
<pre>private&nbsp;pid workflow__k</pre>
</li>
</ul>
<a name="workflow__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflow__v</h4>
<pre>private&nbsp;<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a> workflow__v</pre>
</li>
</ul>
<a name="contents__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contents__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt; contents__q</pre>
</li>
</ul>
<a name="groupNames__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupNames__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a>&gt; groupNames__q</pre>
</li>
</ul>
<a name="grantedSites__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>grantedSites__q</h4>
<pre>private&nbsp;RelQuery&lt;GrantedSiteImpl&gt; grantedSites__q</pre>
</li>
</ul>
<a name="parameters__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parameters__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/ParameterComponentImpl.html" title="class in com.visionnaire.webpublication.business">ParameterComponentImpl</a>&gt; parameters__q</pre>
</li>
</ul>
<a name="childrenComponents__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>childrenComponents__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&gt; childrenComponents__q</pre>
</li>
</ul>
<a name="fatherComponentPid__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherComponentPid__o</h4>
<pre>private&nbsp;pid fatherComponentPid__o</pre>
</li>
</ul>
<a name="fatherComponentPid__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherComponentPid__k</h4>
<pre>private&nbsp;pid fatherComponentPid__k</pre>
</li>
</ul>
<a name="fatherComponentPid__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherComponentPid__v</h4>
<pre>private&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a> fatherComponentPid__v</pre>
</li>
</ul>
<a name="inheritComponents__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inheritComponents__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/InheritComponentImpl.html" title="class in com.visionnaire.webpublication.business">InheritComponentImpl</a>&gt; inheritComponents__q</pre>
</li>
</ul>
<a name="gerararquivos__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerararquivos__v</h4>
<pre>private&nbsp;boolean gerararquivos__v</pre>
</li>
</ul>
<a name="serencontradobusca__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serencontradobusca__v</h4>
<pre>private&nbsp;boolean serencontradobusca__v</pre>
</li>
</ul>
<a name="nickname__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nickname__v</h4>
<pre>private&nbsp;String nickname__v</pre>
</li>
</ul>
<a name="captcha__v">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>captcha__v</h4>
<pre>private&nbsp;boolean captcha__v</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantComponentImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantComponentImpl</h4>
<pre>public&nbsp;PServantComponentImpl(pid&nbsp;id,
                     Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantComponentImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantComponentImpl</h4>
<pre>public&nbsp;PServantComponentImpl(Persistent&nbsp;jobj,
                     pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="componentTitle()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>componentTitle</h4>
<pre>public final&nbsp;String&nbsp;componentTitle()</pre>
</li>
</ul>
<a name="componentTitle(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>componentTitle</h4>
<pre>public final&nbsp;void&nbsp;componentTitle(String&nbsp;componentTitle)</pre>
</li>
</ul>
<a name="name()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>public final&nbsp;String&nbsp;name()</pre>
</li>
</ul>
<a name="name(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>public final&nbsp;void&nbsp;name(String&nbsp;name)</pre>
</li>
</ul>
<a name="column()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>column</h4>
<pre>public final&nbsp;int&nbsp;column()</pre>
</li>
</ul>
<a name="column(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>column</h4>
<pre>public final&nbsp;void&nbsp;column(int&nbsp;column)</pre>
</li>
</ul>
<a name="order()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>order</h4>
<pre>public final&nbsp;int&nbsp;order()</pre>
</li>
</ul>
<a name="order(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>order</h4>
<pre>public final&nbsp;void&nbsp;order(int&nbsp;order)</pre>
</li>
</ul>
<a name="active()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>active</h4>
<pre>public final&nbsp;char&nbsp;active()</pre>
</li>
</ul>
<a name="active(char)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>active</h4>
<pre>public final&nbsp;void&nbsp;active(char&nbsp;active)</pre>
</li>
</ul>
<a name="visible()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>visible</h4>
<pre>public final&nbsp;boolean&nbsp;visible()</pre>
</li>
</ul>
<a name="visible(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>visible</h4>
<pre>public final&nbsp;void&nbsp;visible(boolean&nbsp;visible)</pre>
</li>
</ul>
<a name="paging()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>paging</h4>
<pre>public final&nbsp;int&nbsp;paging()</pre>
</li>
</ul>
<a name="paging(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>paging</h4>
<pre>public final&nbsp;void&nbsp;paging(int&nbsp;paging)</pre>
</li>
</ul>
<a name="template()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>template</h4>
<pre>public final&nbsp;String&nbsp;template()</pre>
</li>
</ul>
<a name="template(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>template</h4>
<pre>public final&nbsp;void&nbsp;template(String&nbsp;template)</pre>
</li>
</ul>
<a name="maximumContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>maximumContent</h4>
<pre>public final&nbsp;int&nbsp;maximumContent()</pre>
</li>
</ul>
<a name="maximumContent(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>maximumContent</h4>
<pre>public final&nbsp;void&nbsp;maximumContent(int&nbsp;maximumContent)</pre>
</li>
</ul>
<a name="protection()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>protection</h4>
<pre>public final&nbsp;int&nbsp;protection()</pre>
</li>
</ul>
<a name="protection(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>protection</h4>
<pre>public final&nbsp;void&nbsp;protection(int&nbsp;protection)</pre>
</li>
</ul>
<a name="type()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>public final&nbsp;String&nbsp;type()</pre>
</li>
</ul>
<a name="type(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>public final&nbsp;void&nbsp;type(String&nbsp;type)</pre>
</li>
</ul>
<a name="imageMiniHeight()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imageMiniHeight</h4>
<pre>public final&nbsp;int&nbsp;imageMiniHeight()</pre>
</li>
</ul>
<a name="imageMiniHeight(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imageMiniHeight</h4>
<pre>public final&nbsp;void&nbsp;imageMiniHeight(int&nbsp;imageMiniHeight)</pre>
</li>
</ul>
<a name="imageMiniWidth()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imageMiniWidth</h4>
<pre>public final&nbsp;int&nbsp;imageMiniWidth()</pre>
</li>
</ul>
<a name="imageMiniWidth(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>imageMiniWidth</h4>
<pre>public final&nbsp;void&nbsp;imageMiniWidth(int&nbsp;imageMiniWidth)</pre>
</li>
</ul>
<a name="emailResponsavel()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emailResponsavel</h4>
<pre>public final&nbsp;String&nbsp;emailResponsavel()</pre>
</li>
</ul>
<a name="emailResponsavel(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emailResponsavel</h4>
<pre>public final&nbsp;void&nbsp;emailResponsavel(String&nbsp;emailResponsavel)</pre>
</li>
</ul>
<a name="netUserCreateContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>netUserCreateContent</h4>
<pre>public final&nbsp;boolean&nbsp;netUserCreateContent()</pre>
</li>
</ul>
<a name="netUserCreateContent(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>netUserCreateContent</h4>
<pre>public final&nbsp;void&nbsp;netUserCreateContent(boolean&nbsp;netUserCreateContent)</pre>
</li>
</ul>
<a name="netUserUpdateContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>netUserUpdateContent</h4>
<pre>public final&nbsp;boolean&nbsp;netUserUpdateContent()</pre>
</li>
</ul>
<a name="netUserUpdateContent(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>netUserUpdateContent</h4>
<pre>public final&nbsp;void&nbsp;netUserUpdateContent(boolean&nbsp;netUserUpdateContent)</pre>
</li>
</ul>
<a name="wiki()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wiki</h4>
<pre>public final&nbsp;boolean&nbsp;wiki()</pre>
</li>
</ul>
<a name="wiki(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wiki</h4>
<pre>public final&nbsp;void&nbsp;wiki(boolean&nbsp;wiki)</pre>
</li>
</ul>
<a name="pagNrItensPorPagina()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagNrItensPorPagina</h4>
<pre>public final&nbsp;int&nbsp;pagNrItensPorPagina()</pre>
</li>
</ul>
<a name="pagNrItensPorPagina(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagNrItensPorPagina</h4>
<pre>public final&nbsp;void&nbsp;pagNrItensPorPagina(int&nbsp;pagNrItensPorPagina)</pre>
</li>
</ul>
<a name="pagOrdenacao()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagOrdenacao</h4>
<pre>public final&nbsp;int&nbsp;pagOrdenacao()</pre>
</li>
</ul>
<a name="pagOrdenacao(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagOrdenacao</h4>
<pre>public final&nbsp;void&nbsp;pagOrdenacao(int&nbsp;pagOrdenacao)</pre>
</li>
</ul>
<a name="pagExibirGrupos()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagExibirGrupos</h4>
<pre>public final&nbsp;boolean&nbsp;pagExibirGrupos()</pre>
</li>
</ul>
<a name="pagExibirGrupos(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagExibirGrupos</h4>
<pre>public final&nbsp;void&nbsp;pagExibirGrupos(boolean&nbsp;pagExibirGrupos)</pre>
</li>
</ul>
<a name="flagFriendlyUrl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flagFriendlyUrl</h4>
<pre>public final&nbsp;boolean&nbsp;flagFriendlyUrl()</pre>
</li>
</ul>
<a name="flagFriendlyUrl(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flagFriendlyUrl</h4>
<pre>public final&nbsp;void&nbsp;flagFriendlyUrl(boolean&nbsp;flagFriendlyUrl)</pre>
</li>
</ul>
<a name="site()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>site</h4>
<pre>public final&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;site()</pre>
</li>
</ul>
<a name="site(com.visionnaire.webpublication.business.SiteImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>site</h4>
<pre>public final&nbsp;void&nbsp;site(<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;site)</pre>
</li>
</ul>
<a name="workflow()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflow</h4>
<pre>public final&nbsp;<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&nbsp;workflow()</pre>
</li>
</ul>
<a name="workflow(com.visionnaire.webpublication.workflow.WorkFlowImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflow</h4>
<pre>public final&nbsp;void&nbsp;workflow(<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&nbsp;workflow)</pre>
</li>
</ul>
<a name="contents()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contents</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/VisualContentImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">VisualContentImpl</a>&gt;&nbsp;contents()</pre>
</li>
</ul>
<a name="contentsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contentsCount</h4>
<pre>public final&nbsp;int&nbsp;contentsCount()</pre>
</li>
</ul>
<a name="groupNames()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupNames</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/GroupNameImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">GroupNameImpl</a>&gt;&nbsp;groupNames()</pre>
</li>
</ul>
<a name="groupNamesCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupNamesCount</h4>
<pre>public final&nbsp;int&nbsp;groupNamesCount()</pre>
</li>
</ul>
<a name="grantedSites()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>grantedSites</h4>
<pre>public final&nbsp;PList&lt;GrantedSiteImpl&gt;&nbsp;grantedSites()</pre>
</li>
</ul>
<a name="grantedSitesCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>grantedSitesCount</h4>
<pre>public final&nbsp;int&nbsp;grantedSitesCount()</pre>
</li>
</ul>
<a name="grantedSitesInsert(com.visionnaire.webpublication.access.GrantedSiteImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>grantedSitesInsert</h4>
<pre>public final&nbsp;void&nbsp;grantedSitesInsert(GrantedSiteImpl&nbsp;grantedSite)</pre>
</li>
</ul>
<a name="grantedSitesRemove(com.visionnaire.webpublication.access.GrantedSiteImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>grantedSitesRemove</h4>
<pre>public final&nbsp;void&nbsp;grantedSitesRemove(GrantedSiteImpl&nbsp;grantedSite)</pre>
</li>
</ul>
<a name="grantedSites(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>grantedSites</h4>
<pre>public final&nbsp;void&nbsp;grantedSites(PList&lt;GrantedSiteImpl&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="parameters()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parameters</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/ParameterComponentImpl.html" title="class in com.visionnaire.webpublication.business">ParameterComponentImpl</a>&gt;&nbsp;parameters()</pre>
</li>
</ul>
<a name="parametersCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parametersCount</h4>
<pre>public final&nbsp;int&nbsp;parametersCount()</pre>
</li>
</ul>
<a name="childrenComponents()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>childrenComponents</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&gt;&nbsp;childrenComponents()</pre>
</li>
</ul>
<a name="childrenComponentsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>childrenComponentsCount</h4>
<pre>public final&nbsp;int&nbsp;childrenComponentsCount()</pre>
</li>
</ul>
<a name="fatherComponentPid()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherComponentPid</h4>
<pre>public final&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;fatherComponentPid()</pre>
</li>
</ul>
<a name="fatherComponentPid(com.visionnaire.webpublication.business.ComponentImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherComponentPid</h4>
<pre>public final&nbsp;void&nbsp;fatherComponentPid(<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&nbsp;fatherComponentPid)</pre>
</li>
</ul>
<a name="inheritComponents()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inheritComponents</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/InheritComponentImpl.html" title="class in com.visionnaire.webpublication.business">InheritComponentImpl</a>&gt;&nbsp;inheritComponents()</pre>
</li>
</ul>
<a name="inheritComponentsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inheritComponentsCount</h4>
<pre>public final&nbsp;int&nbsp;inheritComponentsCount()</pre>
</li>
</ul>
<a name="gerararquivos()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerararquivos</h4>
<pre>public final&nbsp;boolean&nbsp;gerararquivos()</pre>
</li>
</ul>
<a name="gerararquivos(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerararquivos</h4>
<pre>public final&nbsp;void&nbsp;gerararquivos(boolean&nbsp;gerararquivos)</pre>
</li>
</ul>
<a name="serencontradobusca()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serencontradobusca</h4>
<pre>public final&nbsp;boolean&nbsp;serencontradobusca()</pre>
</li>
</ul>
<a name="serencontradobusca(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serencontradobusca</h4>
<pre>public final&nbsp;void&nbsp;serencontradobusca(boolean&nbsp;serencontradobusca)</pre>
</li>
</ul>
<a name="nickname()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nickname</h4>
<pre>public final&nbsp;String&nbsp;nickname()</pre>
</li>
</ul>
<a name="nickname(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nickname</h4>
<pre>public final&nbsp;void&nbsp;nickname(String&nbsp;nickname)</pre>
</li>
</ul>
<a name="captcha()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>captcha</h4>
<pre>public final&nbsp;boolean&nbsp;captcha()</pre>
</li>
</ul>
<a name="captcha(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>captcha</h4>
<pre>public final&nbsp;void&nbsp;captcha(boolean&nbsp;captcha)</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantComponentImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev Class</li>
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantContentStateImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/pss/PServantComponentImpl.html" target="_top">Frames</a></li>
<li><a href="PServantComponentImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
