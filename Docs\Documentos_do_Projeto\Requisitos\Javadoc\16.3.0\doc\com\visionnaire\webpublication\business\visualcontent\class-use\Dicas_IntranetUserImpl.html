<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:27 BRT 2013 -->
<title>Uses of Class com.visionnaire.webpublication.business.visualcontent.Dicas_IntranetUserImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.visionnaire.webpublication.business.visualcontent.Dicas_IntranetUserImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/\class-useDicas_IntranetUserImpl.html" target="_top">Frames</a></li>
<li><a href="Dicas_IntranetUserImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.visionnaire.webpublication.business.visualcontent.Dicas_IntranetUserImpl" class="title">Uses of Class<br>com.visionnaire.webpublication.business.visualcontent.Dicas_IntranetUserImpl</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.visionnaire.webpublication.business.visualcontent">com.visionnaire.webpublication.business.visualcontent</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.visionnaire.webpublication.business.visualcontent.pss">com.visionnaire.webpublication.business.visualcontent.pss</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.visionnaire.webpublication.business.visualcontent">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a> in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/package-summary.html">com.visionnaire.webpublication.business.visualcontent</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/package-summary.html">com.visionnaire.webpublication.business.visualcontent</a> that return types with arguments of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">DicasImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/DicasImpl.html#getDicasIntranetUser()">getDicasIntranetUser</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static List&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">Dicas_IntranetUserImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html#retrieveByFields(com.visionnaire.webpublication.business.visualcontent.DicasImpl, long)">retrieveByFields</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/DicasImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">DicasImpl</a>&nbsp;dica,
                long&nbsp;user)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.visionnaire.webpublication.business.visualcontent.pss">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a> in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-summary.html">com.visionnaire.webpublication.business.visualcontent.pss</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-summary.html">com.visionnaire.webpublication.business.visualcontent.pss</a> with type parameters of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">PServantDicasImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantDicasImpl.html#dicasIntranetUser__q">dicasIntranetUser__q</a></strong></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/package-summary.html">com.visionnaire.webpublication.business.visualcontent.pss</a> that return types with arguments of type <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Dicas_IntranetUserImpl</a>&gt;</code></td>
<td class="colLast"><span class="strong">PServantDicasImpl.</span><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantDicasImpl.html#dicasIntranetUser()">dicasIntranetUser</a></strong>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/Dicas_IntranetUserImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/\class-useDicas_IntranetUserImpl.html" target="_top">Frames</a></li>
<li><a href="Dicas_IntranetUserImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
