<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:22 BRT 2013 -->
<title>PServantSiteImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantSiteImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantSiteImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Prev Class</span></a></li>
<li>Next Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/pss/PServantSiteImpl.html" target="_top">Frames</a></li>
<li><a href="PServantSiteImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.pss</div>
<h2 title="Class PServantSiteImpl" class="title">Class PServantSiteImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantSiteImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantSiteImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#alertEmailTemplate__v">alertEmailTemplate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#commentEmailTemplate__v">commentEmailTemplate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#components__q">components__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#fatherSite__k">fatherSite__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#fatherSite__o">fatherSite__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#fatherSite__v">fatherSite__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#folder__v">folder__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#forumEmailTemplate__v">forumEmailTemplate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#friendEmailTemplate__v">friendEmailTemplate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#gerararquivossite__v">gerararquivossite__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#googleAnalytics__v">googleAnalytics__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#keywords__v">keywords__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;ElementImpl&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#layoutElements__q">layoutElements__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#name__v">name__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#researchEmailTemplate__v">researchEmailTemplate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#restricted__v">restricted__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#root__v">root__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#serencontradobusca__v">serencontradobusca__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#sonSites__q">sonSites__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#url__v">url__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;UserImpl&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#users__q">users__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#wikiEmailTemplate__v">wikiEmailTemplate__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#workflows__q">workflows__q</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#PServantSiteImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantSiteImpl</a></strong>(Persistent&nbsp;jobj,
                pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#PServantSiteImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantSiteImpl</a></strong>(pid&nbsp;id,
                Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#alertEmailTemplate()">alertEmailTemplate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#alertEmailTemplate(java.lang.String)">alertEmailTemplate</a></strong>(String&nbsp;alertEmailTemplate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#commentEmailTemplate()">commentEmailTemplate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#commentEmailTemplate(java.lang.String)">commentEmailTemplate</a></strong>(String&nbsp;commentEmailTemplate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#components()">components</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#componentsCount()">componentsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#fatherSite()">fatherSite</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#fatherSite(com.visionnaire.webpublication.business.SiteImpl)">fatherSite</a></strong>(<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;fatherSite)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#folder()">folder</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#folder(java.lang.String)">folder</a></strong>(String&nbsp;folder)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#forumEmailTemplate()">forumEmailTemplate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#forumEmailTemplate(java.lang.String)">forumEmailTemplate</a></strong>(String&nbsp;forumEmailTemplate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#friendEmailTemplate()">friendEmailTemplate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#friendEmailTemplate(java.lang.String)">friendEmailTemplate</a></strong>(String&nbsp;friendEmailTemplate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#gerararquivossite()">gerararquivossite</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#gerararquivossite(boolean)">gerararquivossite</a></strong>(boolean&nbsp;gerararquivossite)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#googleAnalytics()">googleAnalytics</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#googleAnalytics(java.lang.String)">googleAnalytics</a></strong>(String&nbsp;googleAnalytics)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#keywords()">keywords</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#keywords(java.lang.String)">keywords</a></strong>(String&nbsp;keywords)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;ElementImpl&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#layoutElements()">layoutElements</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#layoutElements(com.visionnaire.PSS.client.PList)">layoutElements</a></strong>(PList&lt;ElementImpl&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#layoutElementsCount()">layoutElementsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#layoutElementsInsert(com.visionnaire.webpublication.business.layout.ElementImpl)">layoutElementsInsert</a></strong>(ElementImpl&nbsp;element)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#layoutElementsRemove(com.visionnaire.webpublication.business.layout.ElementImpl)">layoutElementsRemove</a></strong>(ElementImpl&nbsp;element)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#name()">name</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#name(java.lang.String)">name</a></strong>(String&nbsp;name)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#researchEmailTemplate()">researchEmailTemplate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#researchEmailTemplate(java.lang.String)">researchEmailTemplate</a></strong>(String&nbsp;researchEmailTemplate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#restricted()">restricted</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#restricted(boolean)">restricted</a></strong>(boolean&nbsp;restricted)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#root()">root</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#root(java.lang.String)">root</a></strong>(String&nbsp;root)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#serencontradobusca()">serencontradobusca</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#serencontradobusca(int)">serencontradobusca</a></strong>(int&nbsp;serencontradobusca)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#sonSites()">sonSites</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#sonSitesCount()">sonSitesCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#url()">url</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#url(java.lang.String)">url</a></strong>(String&nbsp;url)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>PList&lt;UserImpl&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#users()">users</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#users(com.visionnaire.PSS.client.PList)">users</a></strong>(PList&lt;UserImpl&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#usersCount()">usersCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#usersInsert(com.visionnaire.webpublication.access.UserImpl)">usersInsert</a></strong>(UserImpl&nbsp;user)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#usersRemove(com.visionnaire.webpublication.access.UserImpl)">usersRemove</a></strong>(UserImpl&nbsp;user)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#wikiEmailTemplate()">wikiEmailTemplate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#wikiEmailTemplate(java.lang.String)">wikiEmailTemplate</a></strong>(String&nbsp;wikiEmailTemplate)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#workflows()">workflows</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#workflows(com.visionnaire.PSS.client.PList)">workflows</a></strong>(PList&lt;<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&gt;&nbsp;all)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#workflowsCount()">workflowsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#workflowsInsert(com.visionnaire.webpublication.workflow.WorkFlowImpl)">workflowsInsert</a></strong>(<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&nbsp;workflow)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantSiteImpl.html#workflowsRemove(com.visionnaire.webpublication.workflow.WorkFlowImpl)">workflowsRemove</a></strong>(<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&nbsp;workflow)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="name__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name__v</h4>
<pre>private&nbsp;String name__v</pre>
</li>
</ul>
<a name="url__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>url__v</h4>
<pre>private&nbsp;String url__v</pre>
</li>
</ul>
<a name="root__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>root__v</h4>
<pre>private&nbsp;String root__v</pre>
</li>
</ul>
<a name="restricted__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>restricted__v</h4>
<pre>private&nbsp;boolean restricted__v</pre>
</li>
</ul>
<a name="folder__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>folder__v</h4>
<pre>private&nbsp;String folder__v</pre>
</li>
</ul>
<a name="googleAnalytics__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>googleAnalytics__v</h4>
<pre>private&nbsp;String googleAnalytics__v</pre>
</li>
</ul>
<a name="keywords__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>keywords__v</h4>
<pre>private&nbsp;String keywords__v</pre>
</li>
</ul>
<a name="researchEmailTemplate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>researchEmailTemplate__v</h4>
<pre>private&nbsp;String researchEmailTemplate__v</pre>
</li>
</ul>
<a name="commentEmailTemplate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentEmailTemplate__v</h4>
<pre>private&nbsp;String commentEmailTemplate__v</pre>
</li>
</ul>
<a name="friendEmailTemplate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>friendEmailTemplate__v</h4>
<pre>private&nbsp;String friendEmailTemplate__v</pre>
</li>
</ul>
<a name="alertEmailTemplate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alertEmailTemplate__v</h4>
<pre>private&nbsp;String alertEmailTemplate__v</pre>
</li>
</ul>
<a name="forumEmailTemplate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forumEmailTemplate__v</h4>
<pre>private&nbsp;String forumEmailTemplate__v</pre>
</li>
</ul>
<a name="wikiEmailTemplate__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wikiEmailTemplate__v</h4>
<pre>private&nbsp;String wikiEmailTemplate__v</pre>
</li>
</ul>
<a name="fatherSite__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherSite__o</h4>
<pre>private&nbsp;pid fatherSite__o</pre>
</li>
</ul>
<a name="fatherSite__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherSite__k</h4>
<pre>private&nbsp;pid fatherSite__k</pre>
</li>
</ul>
<a name="fatherSite__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherSite__v</h4>
<pre>private&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a> fatherSite__v</pre>
</li>
</ul>
<a name="sonSites__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sonSites__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&gt; sonSites__q</pre>
</li>
</ul>
<a name="components__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>components__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&gt; components__q</pre>
</li>
</ul>
<a name="workflows__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflows__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&gt; workflows__q</pre>
</li>
</ul>
<a name="layoutElements__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>layoutElements__q</h4>
<pre>private&nbsp;RelQuery&lt;ElementImpl&gt; layoutElements__q</pre>
</li>
</ul>
<a name="users__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>users__q</h4>
<pre>private&nbsp;RelQuery&lt;UserImpl&gt; users__q</pre>
</li>
</ul>
<a name="gerararquivossite__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerararquivossite__v</h4>
<pre>private&nbsp;boolean gerararquivossite__v</pre>
</li>
</ul>
<a name="serencontradobusca__v">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>serencontradobusca__v</h4>
<pre>private&nbsp;int serencontradobusca__v</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantSiteImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantSiteImpl</h4>
<pre>public&nbsp;PServantSiteImpl(pid&nbsp;id,
                Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantSiteImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantSiteImpl</h4>
<pre>public&nbsp;PServantSiteImpl(Persistent&nbsp;jobj,
                pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="name()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>public final&nbsp;String&nbsp;name()</pre>
</li>
</ul>
<a name="name(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>public final&nbsp;void&nbsp;name(String&nbsp;name)</pre>
</li>
</ul>
<a name="url()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>url</h4>
<pre>public final&nbsp;String&nbsp;url()</pre>
</li>
</ul>
<a name="url(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>url</h4>
<pre>public final&nbsp;void&nbsp;url(String&nbsp;url)</pre>
</li>
</ul>
<a name="root()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>root</h4>
<pre>public final&nbsp;String&nbsp;root()</pre>
</li>
</ul>
<a name="root(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>root</h4>
<pre>public final&nbsp;void&nbsp;root(String&nbsp;root)</pre>
</li>
</ul>
<a name="restricted()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>restricted</h4>
<pre>public final&nbsp;boolean&nbsp;restricted()</pre>
</li>
</ul>
<a name="restricted(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>restricted</h4>
<pre>public final&nbsp;void&nbsp;restricted(boolean&nbsp;restricted)</pre>
</li>
</ul>
<a name="folder()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>folder</h4>
<pre>public final&nbsp;String&nbsp;folder()</pre>
</li>
</ul>
<a name="folder(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>folder</h4>
<pre>public final&nbsp;void&nbsp;folder(String&nbsp;folder)</pre>
</li>
</ul>
<a name="googleAnalytics()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>googleAnalytics</h4>
<pre>public final&nbsp;String&nbsp;googleAnalytics()</pre>
</li>
</ul>
<a name="googleAnalytics(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>googleAnalytics</h4>
<pre>public final&nbsp;void&nbsp;googleAnalytics(String&nbsp;googleAnalytics)</pre>
</li>
</ul>
<a name="keywords()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>keywords</h4>
<pre>public final&nbsp;String&nbsp;keywords()</pre>
</li>
</ul>
<a name="keywords(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>keywords</h4>
<pre>public final&nbsp;void&nbsp;keywords(String&nbsp;keywords)</pre>
</li>
</ul>
<a name="researchEmailTemplate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>researchEmailTemplate</h4>
<pre>public final&nbsp;String&nbsp;researchEmailTemplate()</pre>
</li>
</ul>
<a name="researchEmailTemplate(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>researchEmailTemplate</h4>
<pre>public final&nbsp;void&nbsp;researchEmailTemplate(String&nbsp;researchEmailTemplate)</pre>
</li>
</ul>
<a name="commentEmailTemplate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentEmailTemplate</h4>
<pre>public final&nbsp;String&nbsp;commentEmailTemplate()</pre>
</li>
</ul>
<a name="commentEmailTemplate(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commentEmailTemplate</h4>
<pre>public final&nbsp;void&nbsp;commentEmailTemplate(String&nbsp;commentEmailTemplate)</pre>
</li>
</ul>
<a name="friendEmailTemplate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>friendEmailTemplate</h4>
<pre>public final&nbsp;String&nbsp;friendEmailTemplate()</pre>
</li>
</ul>
<a name="friendEmailTemplate(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>friendEmailTemplate</h4>
<pre>public final&nbsp;void&nbsp;friendEmailTemplate(String&nbsp;friendEmailTemplate)</pre>
</li>
</ul>
<a name="alertEmailTemplate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alertEmailTemplate</h4>
<pre>public final&nbsp;String&nbsp;alertEmailTemplate()</pre>
</li>
</ul>
<a name="alertEmailTemplate(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alertEmailTemplate</h4>
<pre>public final&nbsp;void&nbsp;alertEmailTemplate(String&nbsp;alertEmailTemplate)</pre>
</li>
</ul>
<a name="forumEmailTemplate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forumEmailTemplate</h4>
<pre>public final&nbsp;String&nbsp;forumEmailTemplate()</pre>
</li>
</ul>
<a name="forumEmailTemplate(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>forumEmailTemplate</h4>
<pre>public final&nbsp;void&nbsp;forumEmailTemplate(String&nbsp;forumEmailTemplate)</pre>
</li>
</ul>
<a name="wikiEmailTemplate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wikiEmailTemplate</h4>
<pre>public final&nbsp;String&nbsp;wikiEmailTemplate()</pre>
</li>
</ul>
<a name="wikiEmailTemplate(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wikiEmailTemplate</h4>
<pre>public final&nbsp;void&nbsp;wikiEmailTemplate(String&nbsp;wikiEmailTemplate)</pre>
</li>
</ul>
<a name="fatherSite()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherSite</h4>
<pre>public final&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;fatherSite()</pre>
</li>
</ul>
<a name="fatherSite(com.visionnaire.webpublication.business.SiteImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fatherSite</h4>
<pre>public final&nbsp;void&nbsp;fatherSite(<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&nbsp;fatherSite)</pre>
</li>
</ul>
<a name="sonSites()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sonSites</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business">SiteImpl</a>&gt;&nbsp;sonSites()</pre>
</li>
</ul>
<a name="sonSitesCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sonSitesCount</h4>
<pre>public final&nbsp;int&nbsp;sonSitesCount()</pre>
</li>
</ul>
<a name="components()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>components</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business">ComponentImpl</a>&gt;&nbsp;components()</pre>
</li>
</ul>
<a name="componentsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>componentsCount</h4>
<pre>public final&nbsp;int&nbsp;componentsCount()</pre>
</li>
</ul>
<a name="workflows()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflows</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&gt;&nbsp;workflows()</pre>
</li>
</ul>
<a name="workflowsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflowsCount</h4>
<pre>public final&nbsp;int&nbsp;workflowsCount()</pre>
</li>
</ul>
<a name="workflowsInsert(com.visionnaire.webpublication.workflow.WorkFlowImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflowsInsert</h4>
<pre>public final&nbsp;void&nbsp;workflowsInsert(<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&nbsp;workflow)</pre>
</li>
</ul>
<a name="workflowsRemove(com.visionnaire.webpublication.workflow.WorkFlowImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflowsRemove</h4>
<pre>public final&nbsp;void&nbsp;workflowsRemove(<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&nbsp;workflow)</pre>
</li>
</ul>
<a name="workflows(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workflows</h4>
<pre>public final&nbsp;void&nbsp;workflows(PList&lt;<a href="../../../../../com/visionnaire/webpublication/workflow/WorkFlowImpl.html" title="class in com.visionnaire.webpublication.workflow">WorkFlowImpl</a>&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="layoutElements()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>layoutElements</h4>
<pre>public final&nbsp;PList&lt;ElementImpl&gt;&nbsp;layoutElements()</pre>
</li>
</ul>
<a name="layoutElementsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>layoutElementsCount</h4>
<pre>public final&nbsp;int&nbsp;layoutElementsCount()</pre>
</li>
</ul>
<a name="layoutElementsInsert(com.visionnaire.webpublication.business.layout.ElementImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>layoutElementsInsert</h4>
<pre>public final&nbsp;void&nbsp;layoutElementsInsert(ElementImpl&nbsp;element)</pre>
</li>
</ul>
<a name="layoutElementsRemove(com.visionnaire.webpublication.business.layout.ElementImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>layoutElementsRemove</h4>
<pre>public final&nbsp;void&nbsp;layoutElementsRemove(ElementImpl&nbsp;element)</pre>
</li>
</ul>
<a name="layoutElements(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>layoutElements</h4>
<pre>public final&nbsp;void&nbsp;layoutElements(PList&lt;ElementImpl&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="users()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>users</h4>
<pre>public final&nbsp;PList&lt;UserImpl&gt;&nbsp;users()</pre>
</li>
</ul>
<a name="usersCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>usersCount</h4>
<pre>public final&nbsp;int&nbsp;usersCount()</pre>
</li>
</ul>
<a name="usersInsert(com.visionnaire.webpublication.access.UserImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>usersInsert</h4>
<pre>public final&nbsp;void&nbsp;usersInsert(UserImpl&nbsp;user)</pre>
</li>
</ul>
<a name="usersRemove(com.visionnaire.webpublication.access.UserImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>usersRemove</h4>
<pre>public final&nbsp;void&nbsp;usersRemove(UserImpl&nbsp;user)</pre>
</li>
</ul>
<a name="users(com.visionnaire.PSS.client.PList)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>users</h4>
<pre>public final&nbsp;void&nbsp;users(PList&lt;UserImpl&gt;&nbsp;all)</pre>
</li>
</ul>
<a name="gerararquivossite()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerararquivossite</h4>
<pre>public final&nbsp;boolean&nbsp;gerararquivossite()</pre>
</li>
</ul>
<a name="gerararquivossite(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gerararquivossite</h4>
<pre>public final&nbsp;void&nbsp;gerararquivossite(boolean&nbsp;gerararquivossite)</pre>
</li>
</ul>
<a name="serencontradobusca()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serencontradobusca</h4>
<pre>public final&nbsp;int&nbsp;serencontradobusca()</pre>
</li>
</ul>
<a name="serencontradobusca(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serencontradobusca</h4>
<pre>public final&nbsp;void&nbsp;serencontradobusca(int&nbsp;serencontradobusca)</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantSiteImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/pss/PServantParameterComponentImpl.html" title="class in com.visionnaire.webpublication.business.pss"><span class="strong">Prev Class</span></a></li>
<li>Next Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/pss/PServantSiteImpl.html" target="_top">Frames</a></li>
<li><a href="PServantSiteImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
