<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:23 BRT 2013 -->
<title>ResearchResponseImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="ResearchResponseImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResearchResponseImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" target="_top">Frames</a></li>
<li><a href="ResearchResponseImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent</div>
<h2 title="Class ResearchResponseImpl" class="title">Class ResearchResponseImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>BusinessObjectImpl</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/visionnaire/webpublication/pss/BO.html" title="class in com.visionnaire.webpublication.pss">BO</a></li>
<li>
<ul class="inheritance">
<li>ResearchResponseImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">ResearchResponseImpl</span>
extends <a href="../../../../../com/visionnaire/webpublication/pss/BO.html" title="class in com.visionnaire.webpublication.pss">BO</a></pre>
<dl><dt><span class="strong">Author:</span></dt>
  <dd>Fernando M. Santa Rosa</dd></dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss">PServantResearchResponseImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#ps">ps</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#ResearchResponseImpl(java.util.Date, long, java.lang.String, java.lang.String, java.lang.String, boolean)">ResearchResponseImpl</a></strong>(Date&nbsp;date,
                    long&nbsp;sequence,
                    String&nbsp;value,
                    String&nbsp;remoteIp,
                    String&nbsp;authentication,
                    boolean&nbsp;participated)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#ResearchResponseImpl(com.visionnaire.PSS.pid)">ResearchResponseImpl</a></strong>(pid&nbsp;oid)</code>
<div class="block">Construtor para uso do PSS.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#getAuthentication()">getAuthentication</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>Date</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#getDate()">getDate</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#getRemoteIp()">getRemoteIp</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#getResearchQuestion()">getResearchQuestion</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#getSequence()">getSequence</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#getValue()">getValue</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#isParticipated()">isParticipated</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#pssDeleteFields()">pssDeleteFields</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#remove()">remove</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#setAuthentication(java.lang.String)">setAuthentication</a></strong>(String&nbsp;authentication)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#setDate(java.util.Date)">setDate</a></strong>(Date&nbsp;date)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#setParticipated(boolean)">setParticipated</a></strong>(boolean&nbsp;participated)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#setRemoteIp(java.lang.String)">setRemoteIp</a></strong>(String&nbsp;remoteIp)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#setResearchQuestion(com.visionnaire.webpublication.business.visualcontent.ResearchQuestionImpl)">setResearchQuestion</a></strong>(<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&nbsp;researchQuestion)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#setSequence(long)">setSequence</a></strong>(long&nbsp;sequence)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html#setValue(java.lang.String)">setValue</a></strong>(String&nbsp;value)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_BO">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;<a href="../../../../../com/visionnaire/webpublication/pss/BO.html" title="class in com.visionnaire.webpublication.pss">BO</a></h3>
<code><a href="../../../../../com/visionnaire/webpublication/pss/BO.html#toSQL(java.lang.String)">toSQL</a>, <a href="../../../../../com/visionnaire/webpublication/pss/BO.html#toSQL(java.lang.String, int)">toSQL</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_BusinessObjectImpl">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;BusinessObjectImpl</h3>
<code>getPid, pssServant, setPS</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, getClassId, hashCode, isDying, pssDelete, pssDestroy, pssLock, toInternal, toInternal, toString, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ps">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ps</h4>
<pre>private final&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss">PServantResearchResponseImpl</a> ps</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ResearchResponseImpl(com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ResearchResponseImpl</h4>
<pre>public&nbsp;ResearchResponseImpl(pid&nbsp;oid)</pre>
<div class="block">Construtor para uso do PSS.</div>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>oid</code> - </dd></dl>
</li>
</ul>
<a name="ResearchResponseImpl(java.util.Date, long, java.lang.String, java.lang.String, java.lang.String, boolean)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ResearchResponseImpl</h4>
<pre>public&nbsp;ResearchResponseImpl(Date&nbsp;date,
                    long&nbsp;sequence,
                    String&nbsp;value,
                    String&nbsp;remoteIp,
                    String&nbsp;authentication,
                    boolean&nbsp;participated)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>date</code> - </dd><dd><code>sequence</code> - </dd><dd><code>value</code> - </dd><dd><code>remoteIp</code> - </dd><dd><code>authentication</code> - </dd><dd><code>participated</code> - </dd></dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDate</h4>
<pre>public&nbsp;Date&nbsp;getDate()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>Date</dd></dl>
</li>
</ul>
<a name="getSequence()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSequence</h4>
<pre>public&nbsp;long&nbsp;getSequence()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>long</dd></dl>
</li>
</ul>
<a name="getValue()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;String&nbsp;getValue()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="getRemoteIp()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemoteIp</h4>
<pre>public&nbsp;String&nbsp;getRemoteIp()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="getResearchQuestion()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResearchQuestion</h4>
<pre>public&nbsp;<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&nbsp;getResearchQuestion()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>ResearchQuestionImpl</dd></dl>
</li>
</ul>
<a name="getAuthentication()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAuthentication</h4>
<pre>public&nbsp;String&nbsp;getAuthentication()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>String</dd></dl>
</li>
</ul>
<a name="isParticipated()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isParticipated</h4>
<pre>public&nbsp;boolean&nbsp;isParticipated()</pre>
<dl><dt><span class="strong">Returns:</span></dt><dd>boolean</dd></dl>
</li>
</ul>
<a name="setDate(java.util.Date)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDate</h4>
<pre>public&nbsp;void&nbsp;setDate(Date&nbsp;date)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>date</code> - </dd></dl>
</li>
</ul>
<a name="setSequence(long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSequence</h4>
<pre>public&nbsp;void&nbsp;setSequence(long&nbsp;sequence)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>sequence</code> - </dd></dl>
</li>
</ul>
<a name="setValue(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setValue</h4>
<pre>public&nbsp;void&nbsp;setValue(String&nbsp;value)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>value</code> - </dd></dl>
</li>
</ul>
<a name="setRemoteIp(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemoteIp</h4>
<pre>public&nbsp;void&nbsp;setRemoteIp(String&nbsp;remoteIp)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>remoteIp</code> - </dd></dl>
</li>
</ul>
<a name="setResearchQuestion(com.visionnaire.webpublication.business.visualcontent.ResearchQuestionImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResearchQuestion</h4>
<pre>public&nbsp;void&nbsp;setResearchQuestion(<a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&nbsp;researchQuestion)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>researchQuestion</code> - </dd></dl>
</li>
</ul>
<a name="setAuthentication(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAuthentication</h4>
<pre>public&nbsp;void&nbsp;setAuthentication(String&nbsp;authentication)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>authentication</code> - </dd></dl>
</li>
</ul>
<a name="setParticipated(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParticipated</h4>
<pre>public&nbsp;void&nbsp;setParticipated(boolean&nbsp;participated)</pre>
<dl><dt><span class="strong">Parameters:</span></dt><dd><code>participated</code> - </dd></dl>
</li>
</ul>
<a name="pssDeleteFields()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssDeleteFields</h4>
<pre>public&nbsp;void&nbsp;pssDeleteFields()
                     throws NotDisconnectedException</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>pssDeleteFields</code>&nbsp;in class&nbsp;<code>PersistentImplBase</code></dd>
<dt><span class="strong">Throws:</span></dt>
<dd><code>NotDisconnectedException</code></dd></dl>
</li>
</ul>
<a name="remove()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;void&nbsp;remove()
            throws <a href="../../../../../com/visionnaire/webpublication/business/exception/BusinessException.html" title="class in com.visionnaire.webpublication.business.exception">BusinessException</a></pre>
<dl><dt><span class="strong">Throws:</span></dt>
<dd><code><a href="../../../../../com/visionnaire/webpublication/business/exception/BusinessException.html" title="class in com.visionnaire.webpublication.business.exception">BusinessException</a></code></dd></dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResearchResponseImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../com/visionnaire/webpublication/business/visualcontent/RespostaPDFTransient.html" title="class in com.visionnaire.webpublication.business.visualcontent"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" target="_top">Frames</a></li>
<li><a href="ResearchResponseImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
