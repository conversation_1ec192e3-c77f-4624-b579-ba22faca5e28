<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:26 BRT 2013 -->
<title>com.visionnaire.webpublication.business Class Hierarchy</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="com.visionnaire.webpublication.business Class Hierarchy";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/base/util/package-tree.html">Prev</a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/exception/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package com.visionnaire.webpublication.business</h1>
<span class="strong">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">Object
<ul>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ClippingFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ClippingFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ComponentFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ComponentFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/Constants.html" title="class in com.visionnaire.webpublication.business"><span class="strong">Constants</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/EmailFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">EmailFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/EmailTemplateFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">EmailTemplateFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/HistoryFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">HistoryFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ImageFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ImageFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ImportacaoImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ImportacaoImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/LuceneFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">LuceneFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/NameComponent.html" title="class in com.visionnaire.webpublication.business"><span class="strong">NameComponent</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/NewsletterFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">NewsletterFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ParameterComponentFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ParameterComponentFacade</span></a></li>
<li type="circle">PSSUtil
<ul>
<li type="circle">Catalog
<ul>
<li type="circle">PersistentImplBase (implements Persistent)
<ul>
<li type="circle">BusinessObjectImpl
<ul>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/pss/BO.html" title="class in com.visionnaire.webpublication.pss"><span class="strong">BO</span></a>
<ul>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ComponentImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/HistoryImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">HistoryImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ParameterComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ParameterComponentImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/SiteImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">SiteImpl</span></a></li>
</ul>
</li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ContentStateImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ContentStateImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ContentWorkFlowStateImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ContentWorkFlowStateImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/HistoryWikiImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">HistoryWikiImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/InheritComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">InheritComponentImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/NewsletterImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">NewsletterImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelComponentImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">NewsletterModelComponentImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/NewsletterModelImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">NewsletterModelImpl</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/RSSFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">RSSFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/RSSReaderFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">RSSReaderFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/SiteFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">SiteFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/SubjectFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">SubjectFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/TagFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">TagFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/UploadedFileFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">UploadedFileFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/UserFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">UserFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/ValidationCrmImpl.html" title="class in com.visionnaire.webpublication.business"><span class="strong">ValidationCrmImpl</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/VisualContentFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">VisualContentFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/WidgetFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">WidgetFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/WikiFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">WikiFacade</span></a></li>
<li type="circle"><a href="../../../../com/visionnaire/webpublication/business/WorkFlowFacade.html" title="class in com.visionnaire.webpublication.business"><span class="strong">WorkFlowFacade</span></a></li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/visionnaire/webpublication/base/util/package-tree.html">Prev</a></li>
<li><a href="../../../../com/visionnaire/webpublication/business/exception/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/visionnaire/webpublication/business/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
