<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:24 BRT 2013 -->
<title>PServantPollImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantPollImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantPollImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantNotesImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollJustificationImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html" target="_top">Frames</a></li>
<li><a href="PServantPollImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent.pss</div>
<h2 title="Class PServantPollImpl" class="title">Class PServantPollImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantPollImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantPollImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerFive__v">answerFive__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerFour__v">answerFour__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerOne__v">answerOne__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerSeven__v">answerSeven__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerSix__v">answerSix__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerThree__v">answerThree__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerTwo__v">answerTwo__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/LogPoll.html" title="class in com.visionnaire.webpublication.business.visualcontent">LogPoll</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#logPolls__q">logPolls__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/PollJustificationImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">PollJustificationImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#pollJustifications__q">pollJustifications__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#question__v">question__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private char</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#show__v">show__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private char</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#showJustification__v">showJustification__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerFive__v">totalAnswerFive__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerFour__v">totalAnswerFour__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerOne__v">totalAnswerOne__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerSeven__v">totalAnswerSeven__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerSix__v">totalAnswerSix__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerThree__v">totalAnswerThree__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerTwo__v">totalAnswerTwo__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private char</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#voteRegister__v">voteRegister__v</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#PServantPollImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantPollImpl</a></strong>(Persistent&nbsp;jobj,
                pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#PServantPollImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantPollImpl</a></strong>(pid&nbsp;id,
                Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerFive()">answerFive</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerFive(java.lang.String)">answerFive</a></strong>(String&nbsp;answerFive)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerFour()">answerFour</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerFour(java.lang.String)">answerFour</a></strong>(String&nbsp;answerFour)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerOne()">answerOne</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerOne(java.lang.String)">answerOne</a></strong>(String&nbsp;answerOne)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerSeven()">answerSeven</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerSeven(java.lang.String)">answerSeven</a></strong>(String&nbsp;answerSeven)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerSix()">answerSix</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerSix(java.lang.String)">answerSix</a></strong>(String&nbsp;answerSix)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerThree()">answerThree</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerThree(java.lang.String)">answerThree</a></strong>(String&nbsp;answerThree)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerTwo()">answerTwo</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#answerTwo(java.lang.String)">answerTwo</a></strong>(String&nbsp;answerTwo)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/LogPoll.html" title="class in com.visionnaire.webpublication.business.visualcontent">LogPoll</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#logPolls()">logPolls</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#logPollsCount()">logPollsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/PollJustificationImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">PollJustificationImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#pollJustifications()">pollJustifications</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#pollJustificationsCount()">pollJustificationsCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#question()">question</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#question(java.lang.String)">question</a></strong>(String&nbsp;question)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#show()">show</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#show(char)">show</a></strong>(char&nbsp;show)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#showJustification()">showJustification</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#showJustification(char)">showJustification</a></strong>(char&nbsp;showJustification)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerFive()">totalAnswerFive</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerFive(int)">totalAnswerFive</a></strong>(int&nbsp;totalAnswerFive)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerFour()">totalAnswerFour</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerFour(int)">totalAnswerFour</a></strong>(int&nbsp;totalAnswerFour)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerOne()">totalAnswerOne</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerOne(int)">totalAnswerOne</a></strong>(int&nbsp;totalAnswerOne)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerSeven()">totalAnswerSeven</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerSeven(int)">totalAnswerSeven</a></strong>(int&nbsp;totalAnswerSeven)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerSix()">totalAnswerSix</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerSix(int)">totalAnswerSix</a></strong>(int&nbsp;totalAnswerSix)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerThree()">totalAnswerThree</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerThree(int)">totalAnswerThree</a></strong>(int&nbsp;totalAnswerThree)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerTwo()">totalAnswerTwo</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#totalAnswerTwo(int)">totalAnswerTwo</a></strong>(int&nbsp;totalAnswerTwo)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#voteRegister()">voteRegister</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html#voteRegister(char)">voteRegister</a></strong>(char&nbsp;voteRegister)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="question__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>question__v</h4>
<pre>private&nbsp;String question__v</pre>
</li>
</ul>
<a name="answerOne__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerOne__v</h4>
<pre>private&nbsp;String answerOne__v</pre>
</li>
</ul>
<a name="answerTwo__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerTwo__v</h4>
<pre>private&nbsp;String answerTwo__v</pre>
</li>
</ul>
<a name="answerThree__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerThree__v</h4>
<pre>private&nbsp;String answerThree__v</pre>
</li>
</ul>
<a name="answerFour__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerFour__v</h4>
<pre>private&nbsp;String answerFour__v</pre>
</li>
</ul>
<a name="answerFive__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerFive__v</h4>
<pre>private&nbsp;String answerFive__v</pre>
</li>
</ul>
<a name="answerSix__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerSix__v</h4>
<pre>private&nbsp;String answerSix__v</pre>
</li>
</ul>
<a name="answerSeven__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerSeven__v</h4>
<pre>private&nbsp;String answerSeven__v</pre>
</li>
</ul>
<a name="totalAnswerOne__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerOne__v</h4>
<pre>private&nbsp;int totalAnswerOne__v</pre>
</li>
</ul>
<a name="totalAnswerTwo__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerTwo__v</h4>
<pre>private&nbsp;int totalAnswerTwo__v</pre>
</li>
</ul>
<a name="totalAnswerThree__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerThree__v</h4>
<pre>private&nbsp;int totalAnswerThree__v</pre>
</li>
</ul>
<a name="totalAnswerFour__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerFour__v</h4>
<pre>private&nbsp;int totalAnswerFour__v</pre>
</li>
</ul>
<a name="totalAnswerFive__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerFive__v</h4>
<pre>private&nbsp;int totalAnswerFive__v</pre>
</li>
</ul>
<a name="totalAnswerSix__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerSix__v</h4>
<pre>private&nbsp;int totalAnswerSix__v</pre>
</li>
</ul>
<a name="totalAnswerSeven__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerSeven__v</h4>
<pre>private&nbsp;int totalAnswerSeven__v</pre>
</li>
</ul>
<a name="show__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>show__v</h4>
<pre>private&nbsp;char show__v</pre>
</li>
</ul>
<a name="voteRegister__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>voteRegister__v</h4>
<pre>private&nbsp;char voteRegister__v</pre>
</li>
</ul>
<a name="showJustification__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showJustification__v</h4>
<pre>private&nbsp;char showJustification__v</pre>
</li>
</ul>
<a name="logPolls__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>logPolls__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/LogPoll.html" title="class in com.visionnaire.webpublication.business.visualcontent">LogPoll</a>&gt; logPolls__q</pre>
</li>
</ul>
<a name="pollJustifications__q">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pollJustifications__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/PollJustificationImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">PollJustificationImpl</a>&gt; pollJustifications__q</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantPollImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantPollImpl</h4>
<pre>public&nbsp;PServantPollImpl(pid&nbsp;id,
                Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantPollImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantPollImpl</h4>
<pre>public&nbsp;PServantPollImpl(Persistent&nbsp;jobj,
                pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="question()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>question</h4>
<pre>public final&nbsp;String&nbsp;question()</pre>
</li>
</ul>
<a name="question(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>question</h4>
<pre>public final&nbsp;void&nbsp;question(String&nbsp;question)</pre>
</li>
</ul>
<a name="answerOne()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerOne</h4>
<pre>public final&nbsp;String&nbsp;answerOne()</pre>
</li>
</ul>
<a name="answerOne(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerOne</h4>
<pre>public final&nbsp;void&nbsp;answerOne(String&nbsp;answerOne)</pre>
</li>
</ul>
<a name="answerTwo()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerTwo</h4>
<pre>public final&nbsp;String&nbsp;answerTwo()</pre>
</li>
</ul>
<a name="answerTwo(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerTwo</h4>
<pre>public final&nbsp;void&nbsp;answerTwo(String&nbsp;answerTwo)</pre>
</li>
</ul>
<a name="answerThree()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerThree</h4>
<pre>public final&nbsp;String&nbsp;answerThree()</pre>
</li>
</ul>
<a name="answerThree(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerThree</h4>
<pre>public final&nbsp;void&nbsp;answerThree(String&nbsp;answerThree)</pre>
</li>
</ul>
<a name="answerFour()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerFour</h4>
<pre>public final&nbsp;String&nbsp;answerFour()</pre>
</li>
</ul>
<a name="answerFour(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerFour</h4>
<pre>public final&nbsp;void&nbsp;answerFour(String&nbsp;answerFour)</pre>
</li>
</ul>
<a name="answerFive()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerFive</h4>
<pre>public final&nbsp;String&nbsp;answerFive()</pre>
</li>
</ul>
<a name="answerFive(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerFive</h4>
<pre>public final&nbsp;void&nbsp;answerFive(String&nbsp;answerFive)</pre>
</li>
</ul>
<a name="answerSix()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerSix</h4>
<pre>public final&nbsp;String&nbsp;answerSix()</pre>
</li>
</ul>
<a name="answerSix(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerSix</h4>
<pre>public final&nbsp;void&nbsp;answerSix(String&nbsp;answerSix)</pre>
</li>
</ul>
<a name="answerSeven()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerSeven</h4>
<pre>public final&nbsp;String&nbsp;answerSeven()</pre>
</li>
</ul>
<a name="answerSeven(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>answerSeven</h4>
<pre>public final&nbsp;void&nbsp;answerSeven(String&nbsp;answerSeven)</pre>
</li>
</ul>
<a name="totalAnswerOne()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerOne</h4>
<pre>public final&nbsp;int&nbsp;totalAnswerOne()</pre>
</li>
</ul>
<a name="totalAnswerOne(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerOne</h4>
<pre>public final&nbsp;void&nbsp;totalAnswerOne(int&nbsp;totalAnswerOne)</pre>
</li>
</ul>
<a name="totalAnswerTwo()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerTwo</h4>
<pre>public final&nbsp;int&nbsp;totalAnswerTwo()</pre>
</li>
</ul>
<a name="totalAnswerTwo(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerTwo</h4>
<pre>public final&nbsp;void&nbsp;totalAnswerTwo(int&nbsp;totalAnswerTwo)</pre>
</li>
</ul>
<a name="totalAnswerThree()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerThree</h4>
<pre>public final&nbsp;int&nbsp;totalAnswerThree()</pre>
</li>
</ul>
<a name="totalAnswerThree(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerThree</h4>
<pre>public final&nbsp;void&nbsp;totalAnswerThree(int&nbsp;totalAnswerThree)</pre>
</li>
</ul>
<a name="totalAnswerFour()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerFour</h4>
<pre>public final&nbsp;int&nbsp;totalAnswerFour()</pre>
</li>
</ul>
<a name="totalAnswerFour(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerFour</h4>
<pre>public final&nbsp;void&nbsp;totalAnswerFour(int&nbsp;totalAnswerFour)</pre>
</li>
</ul>
<a name="totalAnswerFive()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerFive</h4>
<pre>public final&nbsp;int&nbsp;totalAnswerFive()</pre>
</li>
</ul>
<a name="totalAnswerFive(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerFive</h4>
<pre>public final&nbsp;void&nbsp;totalAnswerFive(int&nbsp;totalAnswerFive)</pre>
</li>
</ul>
<a name="totalAnswerSix()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerSix</h4>
<pre>public final&nbsp;int&nbsp;totalAnswerSix()</pre>
</li>
</ul>
<a name="totalAnswerSix(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerSix</h4>
<pre>public final&nbsp;void&nbsp;totalAnswerSix(int&nbsp;totalAnswerSix)</pre>
</li>
</ul>
<a name="totalAnswerSeven()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerSeven</h4>
<pre>public final&nbsp;int&nbsp;totalAnswerSeven()</pre>
</li>
</ul>
<a name="totalAnswerSeven(int)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalAnswerSeven</h4>
<pre>public final&nbsp;void&nbsp;totalAnswerSeven(int&nbsp;totalAnswerSeven)</pre>
</li>
</ul>
<a name="show()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>show</h4>
<pre>public final&nbsp;char&nbsp;show()</pre>
</li>
</ul>
<a name="show(char)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>show</h4>
<pre>public final&nbsp;void&nbsp;show(char&nbsp;show)</pre>
</li>
</ul>
<a name="voteRegister()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>voteRegister</h4>
<pre>public final&nbsp;char&nbsp;voteRegister()</pre>
</li>
</ul>
<a name="voteRegister(char)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>voteRegister</h4>
<pre>public final&nbsp;void&nbsp;voteRegister(char&nbsp;voteRegister)</pre>
</li>
</ul>
<a name="showJustification()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showJustification</h4>
<pre>public final&nbsp;char&nbsp;showJustification()</pre>
</li>
</ul>
<a name="showJustification(char)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showJustification</h4>
<pre>public final&nbsp;void&nbsp;showJustification(char&nbsp;showJustification)</pre>
</li>
</ul>
<a name="logPolls()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>logPolls</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/LogPoll.html" title="class in com.visionnaire.webpublication.business.visualcontent">LogPoll</a>&gt;&nbsp;logPolls()</pre>
</li>
</ul>
<a name="logPollsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>logPollsCount</h4>
<pre>public final&nbsp;int&nbsp;logPollsCount()</pre>
</li>
</ul>
<a name="pollJustifications()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pollJustifications</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/PollJustificationImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">PollJustificationImpl</a>&gt;&nbsp;pollJustifications()</pre>
</li>
</ul>
<a name="pollJustificationsCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pollJustificationsCount</h4>
<pre>public final&nbsp;int&nbsp;pollJustificationsCount()</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantPollImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantNotesImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantPollJustificationImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantPollImpl.html" target="_top">Frames</a></li>
<li><a href="PServantPollImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
