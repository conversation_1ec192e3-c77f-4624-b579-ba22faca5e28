<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="pt">
<head>
<!-- Generated by javadoc (version 1.7.0) on Wed May 22 14:02:24 BRT 2013 -->
<title>PServantResearchQuestionImpl</title>
<meta name="date" content="2013-05-22">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="PServantResearchQuestionImpl";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantResearchQuestionImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html" target="_top">Frames</a></li>
<li><a href="PServantResearchQuestionImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.visionnaire.webpublication.business.visualcontent.pss</div>
<h2 title="Class PServantResearchQuestionImpl" class="title">Class PServantResearchQuestionImpl</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>Object</li>
<li>
<ul class="inheritance">
<li>PSSUtil</li>
<li>
<ul class="inheritance">
<li>Catalog</li>
<li>
<ul class="inheritance">
<li>PersistentImplBase</li>
<li>
<ul class="inheritance">
<li>StorageObjectBase</li>
<li>
<ul class="inheritance">
<li>PServantResearchQuestionImpl</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>Persistent</dd>
</dl>
<hr>
<br>
<pre>public class <span class="strong">PServantResearchQuestionImpl</span>
extends StorageObjectBase</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#condition__v">condition__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#klass__v">klass__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#number__v">number__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#options__v">options__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#question__v">question__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#questionChildren__q">questionChildren__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#questionFather__k">questionFather__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#questionFather__o">questionFather__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#questionFather__v">questionFather__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#required__v">required__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#research__k">research__k</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#research__o">research__o</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private <a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#research__v">research__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchResponseImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#researchResponses__q">researchResponses__q</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>private String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#type__v">type__v</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>private boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#uniqueResponse__v">uniqueResponse__v</a></strong></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;StorageObjectBase</h3>
<code>pssId, pssObj, SERVANT_VERSION</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;PSSUtil</h3>
<code>OBJECTBL_INIT_CAPACITY, URL_ID_BASE, URL_ID_SEP</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#PServantResearchQuestionImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">PServantResearchQuestionImpl</a></strong>(Persistent&nbsp;jobj,
                            pid&nbsp;klass)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#PServantResearchQuestionImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">PServantResearchQuestionImpl</a></strong>(pid&nbsp;id,
                            Persistent&nbsp;jobj)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method_summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span>Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#condition()">condition</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#condition(java.lang.String)">condition</a></strong>(String&nbsp;condition)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>pid</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#getClassId()">getClassId</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#number()">number</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#number(java.lang.String)">number</a></strong>(String&nbsp;number)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#options()">options</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#options(java.lang.String)">options</a></strong>(String&nbsp;options)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#pssMappedClassName()">pssMappedClassName</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#pssMarshall(java.lang.Object[])">pssMarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#pssMarshallSize()">pssMarshallSize</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#pssUnmarshall(java.lang.Object[])">pssUnmarshall</a></strong>(Object[]&nbsp;attribs)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#question()">question</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#question(java.lang.String)">question</a></strong>(String&nbsp;question)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#questionChildren()">questionChildren</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#questionChildrenCount()">questionChildrenCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#questionFather()">questionFather</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#questionFather(com.visionnaire.webpublication.business.visualcontent.ResearchQuestionImpl)">questionFather</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&nbsp;questionFather)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#required()">required</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#required(boolean)">required</a></strong>(boolean&nbsp;required)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a></code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#research()">research</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#research(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">research</a></strong>(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchResponseImpl</a>&gt;</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#researchResponses()">researchResponses</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#researchResponsesCount()">researchResponsesCount</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>String</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#type()">type</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#type(java.lang.String)">type</a></strong>(String&nbsp;type)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#uniqueResponse()">uniqueResponse</a></strong>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><strong><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html#uniqueResponse(boolean)">uniqueResponse</a></strong>(boolean&nbsp;uniqueResponse)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_StorageObjectBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;StorageObjectBase</h3>
<code>getPid, getVersion, incVersion, pssCompare, pssGetObj, pssLock, pssMarshall, pssPrevServant, pssServant, pssSetDirtyInsert, pssSetDirtyUpdate, pssSetDirtyUpdate, pssSetPid, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PersistentImplBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PersistentImplBase</h3>
<code>checkOwner, checkOwner, checkOwners, checkOwners, checkOwners, checkOwners, equals, hashCode, isDying, pssDelete, pssDeleteFields, pssDestroy, toInternal, toInternal, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Catalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Catalog</h3>
<code>begin, begin, closeConnection, commit, commit, findByPid, findByPid, findByPid, flush, getConnection, getDeleteMode, getTXIsolation, isTXReadOnly, rollback, setDeleteMode, setTXIsolation, setTXReadOnly</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_PSSUtil">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;PSSUtil</h3>
<code>classId, parseUrlClassId, parseUrlPid, pid, pssDelete, pssDelete, pssDelete, toPids, urlId, urlId, urlId</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods_inherited_from_class_Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field_detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="klass__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>klass__v</h4>
<pre>protected&nbsp;pid klass__v</pre>
</li>
</ul>
<a name="number__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>number__v</h4>
<pre>private&nbsp;String number__v</pre>
</li>
</ul>
<a name="question__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>question__v</h4>
<pre>private&nbsp;String question__v</pre>
</li>
</ul>
<a name="type__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type__v</h4>
<pre>private&nbsp;String type__v</pre>
</li>
</ul>
<a name="options__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>options__v</h4>
<pre>private&nbsp;String options__v</pre>
</li>
</ul>
<a name="required__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>required__v</h4>
<pre>private&nbsp;boolean required__v</pre>
</li>
</ul>
<a name="condition__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>condition__v</h4>
<pre>private&nbsp;String condition__v</pre>
</li>
</ul>
<a name="uniqueResponse__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uniqueResponse__v</h4>
<pre>private&nbsp;boolean uniqueResponse__v</pre>
</li>
</ul>
<a name="research__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research__o</h4>
<pre>private&nbsp;pid research__o</pre>
</li>
</ul>
<a name="research__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research__k</h4>
<pre>private&nbsp;pid research__k</pre>
</li>
</ul>
<a name="research__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a> research__v</pre>
</li>
</ul>
<a name="researchResponses__q">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>researchResponses__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchResponseImpl</a>&gt; researchResponses__q</pre>
</li>
</ul>
<a name="questionFather__o">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>questionFather__o</h4>
<pre>private&nbsp;pid questionFather__o</pre>
</li>
</ul>
<a name="questionFather__k">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>questionFather__k</h4>
<pre>private&nbsp;pid questionFather__k</pre>
</li>
</ul>
<a name="questionFather__v">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>questionFather__v</h4>
<pre>private&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a> questionFather__v</pre>
</li>
</ul>
<a name="questionChildren__q">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>questionChildren__q</h4>
<pre>private&nbsp;RelQuery&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&gt; questionChildren__q</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor_detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PServantResearchQuestionImpl(com.visionnaire.PSS.pid, com.visionnaire.PSS.client.Persistent)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PServantResearchQuestionImpl</h4>
<pre>public&nbsp;PServantResearchQuestionImpl(pid&nbsp;id,
                            Persistent&nbsp;jobj)</pre>
</li>
</ul>
<a name="PServantResearchQuestionImpl(com.visionnaire.PSS.client.Persistent, com.visionnaire.PSS.pid)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PServantResearchQuestionImpl</h4>
<pre>public&nbsp;PServantResearchQuestionImpl(Persistent&nbsp;jobj,
                            pid&nbsp;klass)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method_detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="pssMappedClassName()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMappedClassName</h4>
<pre>protected&nbsp;String&nbsp;pssMappedClassName()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMappedClassName</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="getClassId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassId</h4>
<pre>public final&nbsp;pid&nbsp;getClassId()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>getClassId</code>&nbsp;in interface&nbsp;<code>Persistent</code></dd>
<dt><strong>Overrides:</strong></dt>
<dd><code>getClassId</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="number()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>number</h4>
<pre>public final&nbsp;String&nbsp;number()</pre>
</li>
</ul>
<a name="number(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>number</h4>
<pre>public final&nbsp;void&nbsp;number(String&nbsp;number)</pre>
</li>
</ul>
<a name="question()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>question</h4>
<pre>public final&nbsp;String&nbsp;question()</pre>
</li>
</ul>
<a name="question(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>question</h4>
<pre>public final&nbsp;void&nbsp;question(String&nbsp;question)</pre>
</li>
</ul>
<a name="type()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>public final&nbsp;String&nbsp;type()</pre>
</li>
</ul>
<a name="type(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>public final&nbsp;void&nbsp;type(String&nbsp;type)</pre>
</li>
</ul>
<a name="options()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>options</h4>
<pre>public final&nbsp;String&nbsp;options()</pre>
</li>
</ul>
<a name="options(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>options</h4>
<pre>public final&nbsp;void&nbsp;options(String&nbsp;options)</pre>
</li>
</ul>
<a name="required()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>required</h4>
<pre>public final&nbsp;boolean&nbsp;required()</pre>
</li>
</ul>
<a name="required(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>required</h4>
<pre>public final&nbsp;void&nbsp;required(boolean&nbsp;required)</pre>
</li>
</ul>
<a name="condition()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>condition</h4>
<pre>public final&nbsp;String&nbsp;condition()</pre>
</li>
</ul>
<a name="condition(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>condition</h4>
<pre>public final&nbsp;void&nbsp;condition(String&nbsp;condition)</pre>
</li>
</ul>
<a name="uniqueResponse()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uniqueResponse</h4>
<pre>public final&nbsp;boolean&nbsp;uniqueResponse()</pre>
</li>
</ul>
<a name="uniqueResponse(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uniqueResponse</h4>
<pre>public final&nbsp;void&nbsp;uniqueResponse(boolean&nbsp;uniqueResponse)</pre>
</li>
</ul>
<a name="research()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research()</pre>
</li>
</ul>
<a name="research(com.visionnaire.webpublication.business.visualcontent.ResearchImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>research</h4>
<pre>public final&nbsp;void&nbsp;research(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchImpl</a>&nbsp;research)</pre>
</li>
</ul>
<a name="researchResponses()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>researchResponses</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchResponseImpl</a>&gt;&nbsp;researchResponses()</pre>
</li>
</ul>
<a name="researchResponsesCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>researchResponsesCount</h4>
<pre>public final&nbsp;int&nbsp;researchResponsesCount()</pre>
</li>
</ul>
<a name="questionFather()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>questionFather</h4>
<pre>public final&nbsp;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&nbsp;questionFather()</pre>
</li>
</ul>
<a name="questionFather(com.visionnaire.webpublication.business.visualcontent.ResearchQuestionImpl)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>questionFather</h4>
<pre>public final&nbsp;void&nbsp;questionFather(<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&nbsp;questionFather)</pre>
</li>
</ul>
<a name="questionChildren()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>questionChildren</h4>
<pre>public final&nbsp;PList&lt;<a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/ResearchQuestionImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent">ResearchQuestionImpl</a>&gt;&nbsp;questionChildren()</pre>
</li>
</ul>
<a name="questionChildrenCount()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>questionChildrenCount</h4>
<pre>public final&nbsp;int&nbsp;questionChildrenCount()</pre>
</li>
</ul>
<a name="pssMarshallSize()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshallSize</h4>
<pre>protected&nbsp;int&nbsp;pssMarshallSize()</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshallSize</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssMarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pssMarshall</h4>
<pre>public&nbsp;void&nbsp;pssMarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssMarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
<a name="pssUnmarshall(java.lang.Object[])">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>pssUnmarshall</h4>
<pre>public&nbsp;void&nbsp;pssUnmarshall(Object[]&nbsp;attribs)</pre>
<dl>
<dt><strong>Specified by:</strong></dt>
<dd><code>pssUnmarshall</code>&nbsp;in class&nbsp;<code>StorageObjectBase</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PServantResearchQuestionImpl.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-files/index-1.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Prev Class</span></a></li>
<li><a href="../../../../../../com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchResponseImpl.html" title="class in com.visionnaire.webpublication.business.visualcontent.pss"><span class="strong">Next Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/visionnaire/webpublication/business/visualcontent/pss/PServantResearchQuestionImpl.html" target="_top">Frames</a></li>
<li><a href="PServantResearchQuestionImpl.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field_summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field_detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor_detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method_detail">Method</a></li>
</ul>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
